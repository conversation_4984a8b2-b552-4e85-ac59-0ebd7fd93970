package com.optimaitalia.controller;

import com.optima.common.exceptions.ValidateException;
import com.optima.security.model.JwtToken;
import com.optimaitalia.exception.PaymentException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.security.Principal;
import java.util.Map;

@ControllerAdvice
public class GlobalDefaultExceptionHandler {

    private static final Logger logger = LogManager.getLogger(GlobalDefaultExceptionHandler.class);

    @Value("${payment.filed.url}")
    private String paymentFailedUrl;


    @ExceptionHandler(value = ValidateException.class)
    @ResponseBody
    protected Map<String, String> validationExceptionHandler(ValidateException e) {
        return e.getErrorsMap();
    }

    @ExceptionHandler(value = PaymentException.class)
    @ResponseBody
    protected String paymentExceptionHandler(PaymentException exception, Principal principal) {
        logger.error("Error while trying to obtain payment usr for user with id {}. Error: {}",
                ((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid(), exception);
        return exception.getMessage() + " error";
    }

}
