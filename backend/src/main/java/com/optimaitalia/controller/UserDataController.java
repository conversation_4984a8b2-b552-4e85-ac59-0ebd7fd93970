package com.optimaitalia.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.model.condominio.Condominio;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventResponse;
import com.optimaitalia.model.wrappers.offer.ClientBundleDetails;
import com.optimaitalia.model.wrappers.offer.InitialBonusProgress;
import com.optimaitalia.model.wrappers.offer.PromoMeseOffWrapper;
import com.optimaitalia.model.wrappers.offer.request.ChangePromoMeseRequest;
import com.optimaitalia.model.wrappers.offer.request.CheckTariffRequest;
import com.optimaitalia.model.wrappers.user.requests.ChangeAddressRequest;
import com.optimaitalia.model.wrappers.user.requests.ChangePasswordIdentificationRequest;
import com.optimaitalia.model.wrappers.user.requests.PersonalDataChangeRequest;
import com.optimaitalia.model.wrappers.user.response.ChangePersonalDataResponse;
import com.optimaitalia.service.OffersService;
import com.optimaitalia.service.UserDataService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/user")
public class UserDataController {

    private final UserDataService userDataService;

    private final OffersService offersService;

    public UserDataController(UserDataService userDataService, OffersService offersService) {
        this.userDataService = userDataService;
        this.offersService = offersService;
    }

    @PostMapping("/change")
    @PreAuthorize("#request.clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.clientId, authentication.principal.uid)")
    public ChangePersonalDataResponse changePersonalUserData(@RequestBody PersonalDataChangeRequest request) throws ValidateException {
        return userDataService.changePersonalUserData(request);
    }

    @PostMapping("/change/password-identification")
    @PreAuthorize("#request.customerId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.customerId, authentication.principal.uid)")
    public ChangePersonalDataResponse changePasswordIdentification(@RequestBody ChangePasswordIdentificationRequest request) {
        return userDataService.changePasswordIdentification(request);
    }

    @PostMapping("/change/address")
    @PreAuthorize("#request.clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.clientId, authentication.principal.uid)")
    public IncidentEventResponse changeBillingAddress(@RequestBody ChangeAddressRequest request) throws ValidateException {
        return userDataService.changeBillingAddress(request);
    }

    @GetMapping("/initial/bonus/{clientId}/{billingId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public InitialBonusProgress usersInitialBonuses(@PathVariable String clientId, @PathVariable String billingId) {
        return offersService.loadInitialBonusProgress(clientId, billingId);
    }

    @GetMapping("/offer/bundle/details/{clientId}/{billingId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<ClientBundleDetails> governanceOfferClientBundleDetails(@PathVariable String clientId, @PathVariable String billingId) {
        return offersService.getClientBundleDetails(clientId, billingId);
    }

    @GetMapping("/check/tariff")
    public ResponseEntity checkYourTariff(CheckTariffRequest checkTariffRequest) throws ValidateException {
        return offersService.checkYourTariff(checkTariffRequest);
    }

    @GetMapping("/promo/mese/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<PromoMeseOffWrapper> promoMeseOff(@PathVariable String clientId) {
        return offersService.promoMeseOff(clientId);
    }

    @PostMapping("/promo/mese")
    @PreAuthorize("#request.customerId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.customerId, authentication.principal.uid)")
    public ChangePersonalDataResponse changePromoMeseOff(@RequestBody ChangePromoMeseRequest request) throws JsonProcessingException {
        return offersService.changePromoMeseOff(request);
    }

    @GetMapping("/condominio/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<Condominio> getUserCondominioInfo(@PathVariable String clientId) {
        return userDataService.getUserCondominioInfo(clientId);
    }

    @GetMapping("/condominio/fiscal-code/{clientId}")
    public ResponseEntity<?> getUserCondominioFiscalCode(@PathVariable String clientId) {
        return userDataService.getUserCondominioFiscalCode(clientId);
    }
}
