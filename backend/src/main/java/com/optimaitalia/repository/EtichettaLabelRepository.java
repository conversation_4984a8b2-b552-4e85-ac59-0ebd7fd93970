package com.optimaitalia.repository;

import com.optimaitalia.model.db.EtichettaLabel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface EtichettaLabelRepository  extends JpaRepository<EtichettaLabel, Long> {

    @Query(value = "SELECT l from EtichettaLabel l WHERE l.podUtNumber = ?1")
    EtichettaLabel getEtichettaLabelByPodUtNumber(String pod);
}
