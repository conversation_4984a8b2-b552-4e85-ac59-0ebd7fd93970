package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optimaitalia.model.enums.IncidentEvent;
import com.optimaitalia.model.enums.IncidentEventCategory;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventRequest;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventResponse;
import com.optimaitalia.model.wrappers.support.RecontactRequest;
import com.optimaitalia.service.IncidentEventService;
import com.optimaitalia.service.SegnalazioneService;
import com.optimaitalia.service.SupportService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class SupportServiceImpl implements SupportService {

    private final IncidentEventService incidentEventService;

    private final RestTemplate restTemplate;

    private final SegnalazioneService segnalazioneService;
    private final OvalValidator ovalValidator;

    @Value("${restdata.urls.recontact-request-url}")
    private String recontactRequestUrl;

    public SupportServiceImpl(IncidentEventService incidentEventService, RestTemplate restTemplate, SegnalazioneService segnalazioneService, OvalValidator ovalValidator) {
        this.incidentEventService = incidentEventService;
        this.restTemplate = restTemplate;
        this.segnalazioneService = segnalazioneService;
        this.ovalValidator = ovalValidator;
    }

    @Override
    public ResponseEntity recontactRequest(RecontactRequest recontactRequest) throws ValidateException {
        ovalValidator.validate(recontactRequest);
        IncidentEventResponse incidentEventResponse = recontactIncidentEvent(recontactRequest);
        if (incidentEventResponse != null && "OK".equals(incidentEventResponse.getStatus())) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.ACCEPT_ENCODING, "*");
            return new ResponseEntity<>("", restTemplate.exchange(recontactRequestUrl, HttpMethod.POST,
                    new HttpEntity<>(recontactRequest, headers), Map.class).getStatusCode());
        }
        return ResponseEntity.noContent().build();
    }

    @Override
    public IncidentEventResponse recontactIncidentEvent(RecontactRequest recontactRequest) throws ValidateException {
        String annotationDelimiter = " / ";
        IncidentEventRequest incidentEvent = new IncidentEventRequest();
        incidentEvent.setCustomerId(recontactRequest.getCustomerId());
        incidentEvent.setIncidentEvent(IncidentEvent.RECONTACT_REQUEST);
        incidentEvent.setIncidentCategory(IncidentEventCategory.DASH_BUTTON);
        incidentEvent.setServiceType("0");
        String builder = recontactRequest.getCustomerId() + annotationDelimiter +
                recontactRequest.getPhoneNumber() + annotationDelimiter +
                recontactRequest.getCampaignId().getDescription() + annotationDelimiter +
                new SimpleDateFormat("dd/MM/yyyy").format(new Date()) + annotationDelimiter +
                recontactRequest.getSkillSet().getDescription() + annotationDelimiter +
                recontactRequest.getMessage();
        incidentEvent.setIncidentAnnotation(builder);
        incidentEvent.setOrigin("200007");
        return incidentEventService.customerIncidentEvent(incidentEvent);
    }

    @Override
    public Boolean checkIncidentEvent(String clientId) throws JsonProcessingException {
        List incident = segnalazioneService.getIncidentCheck(clientId);
        return incident.stream().filter(item -> ((LinkedHashMap) item).get("StateCode").equals(0) &&
                        ((LinkedHashMap) item).get("Title").equals("Informazione -> Commerciale -> Richiesta ricontatto"))
                .count() == 0;
    }

    @Override
    public Boolean checkIncidentEventChangePromoMese(String clientId) throws JsonProcessingException {
        List incident = segnalazioneService.getIncidentCheck(clientId);

        if (incident.size() == 0) {
            return false;
        } else {
            return incident.stream()
                    .filter(item -> ((LinkedHashMap) item).get("StateCode").equals(0) &&
                            (//((LinkedHashMap) item).get("Title").equals("Informazione -> Commerciale -> Richiesta Ricontatto") ||
                                    ((LinkedHashMap) item).get("Title").equals("Variazione -> Commerciale -> Fatturazione"))
                            && ((LinkedHashMap) item).get("Opt_CaratterizzazioneDescrizione").equals("Promo 3 mesi"))
                    .count() >= 1;
        }
    }

    @Override
    public Boolean checkIncidentEventDifferentType(String clientId) throws JsonProcessingException {
        List incident = segnalazioneService.getIncidentCheck(clientId);

        if (incident.size() == 0) {
            return false;
        } else {
            return incident.stream()
                    .filter(item -> ((LinkedHashMap) item).get("StateCode").equals(1) &&
                            (((LinkedHashMap) item).get("Title").equals("Variazione -> Commerciale -> Agevolazione"))
                            && (((LinkedHashMap) item).get("Opt_CaratterizzazioneDescrizione") != null) &&
                            (((LinkedHashMap) item).get("Opt_CaratterizzazioneDescrizione").equals("Sordo") ||
                            ((LinkedHashMap) item).get("Opt_CaratterizzazioneDescrizione").equals("Cieco") ||
                            ((LinkedHashMap) item).get("Opt_CaratterizzazioneDescrizione").equals("Cieco Parziale") ||
                            ((LinkedHashMap) item).get("Opt_CaratterizzazioneDescrizione").equals("Non Deambulante")))
                    .count() >= 1;
        }
    }

    @Override
    public Boolean checkIncidentRichiestaPortabilita(String clientId,  String msisdnId) throws JsonProcessingException {
        List incident = segnalazioneService.getIncidentCheck(clientId);
        return incident.stream().filter(item -> ((LinkedHashMap) item).get("StateCode").equals(0) &&
//                ((LinkedHashMap) item).get("Annotation").toString().contains(msisdnId) &&
                ((LinkedHashMap) item).get("Title").equals("Variazione -> Tecnico -> MNP IN Post Attivazione") ||
                ((LinkedHashMap) item).get("Title").equals("Variazione -> Tecnico -> MNP IN in stato attivo"))
                .count() < 1;
    }

    @Override
    public Boolean checkIncidentEventChangeDateScandenza(String clientId) throws JsonProcessingException {
        List incident = segnalazioneService.getIncidentCheck(clientId);
        if (incident.isEmpty()) {
            return true;
        }
        return incident.stream().filter(item -> ((LinkedHashMap) item).get("StateCode").equals(0) &&
                ((LinkedHashMap) item).get("Title").equals("Variazione -> Amministrativo -> Fatturazione"))
                .count() < 1;
    }


    @Override
    public Boolean checkOpenIncidentEventPagamentoFlessibile(String clientId) throws JsonProcessingException {
        List incident = segnalazioneService.getIncidentCheck(clientId);
        if (incident.isEmpty()) {
            return true;
        }
        return incident.stream().filter(item -> ((LinkedHashMap) item).get("StateCode").equals(0) &&
                        ((LinkedHashMap) item).get("Opt_CaratterizzazioneDescrizione").equals("Pagamento Flessibile"))
                .count() < 1;
    }

    @Override
    public Boolean checkOpenIncidentEventCrossSelling(String clientId) throws JsonProcessingException {
        List incident = segnalazioneService.getIncidentCheck(clientId);
        if (incident.isEmpty()) {
            return true;
        }
        return incident.stream().filter(item -> ((LinkedHashMap) item).get("StateCode").equals(0) &&
                        ((LinkedHashMap) item).get("Title").equals("Informazione -> Commerciale -> CrossSelling"))
                .count() < 1;
    }
}
