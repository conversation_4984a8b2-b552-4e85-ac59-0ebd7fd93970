package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.segnalazione.SiganlPostModel;
import com.optimaitalia.service.SegnalazioneService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.time.ZonedDateTime;
import java.util.*;

@Service
public class SegnalazioneServiceImpl implements SegnalazioneService {

    private static final Logger logger = LogManager.getLogger(SegnalazioneServiceImpl.class);

    private @Value("${restdata.urls.segnalazione}")
    String signalUrl;

    private final RestTemplate restTemplate;

    private final SecurityService securityService;

    private final ObjectMapper objectMapper;

    public SegnalazioneServiceImpl(RestTemplate restTemplate, SecurityService securityService, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.securityService = securityService;
        this.objectMapper = objectMapper;
    }

    @Override
    public List<Object> getSignals(String clientId) throws JsonProcessingException {
        return getSignals(clientId, setTripetForSignals());
    }

    @Override
    public List<Object> getIncidentCheck(String clientId) throws JsonProcessingException {
        return getSignals(clientId, setTripetForSignals());
    }


    private List<Object> getSignals(String clientId, List<Object> list) throws JsonProcessingException {
        logger.info("Obtaining list of segnalazione date for user with id {}", clientId);
        Date beforeDate = new Date();
        Date afterDate = Date.from(ZonedDateTime.now().minusMonths(1).toInstant());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        headers.set("tenant", "Optimaitalia");
        Map<String, Object> body = new HashMap<>();
        List<Object> object1 = new ArrayList<>();
        List<Object> origineCasoItems = new ArrayList<>();
        List<Object> stateCode = new ArrayList<>();
        origineCasoItems.add("chat");
        origineCasoItems.add("chatbot");
        origineCasoItems.add("fax");
        origineCasoItems.add("ivr");
        origineCasoItems.add("pec");
        origineCasoItems.add("posta elettronica");
        origineCasoItems.add("raccomandata");
        origineCasoItems.add("segnalazione in sede");
        origineCasoItems.add("selfcare");
        origineCasoItems.add("social");
        origineCasoItems.add("telefonata in entrata");
        origineCasoItems.add("web");
        origineCasoItems.add("APP MyOptima");

        stateCode.add("0");
        stateCode.add("1");

        object1.add(new SiganlPostModel("Opt_OrigineCasoDescrizione", "IN", origineCasoItems, false));
        object1.add(new SiganlPostModel("CreatedOn", "GREATER", getPartOfDate(afterDate.toInstant().toString())));
        object1.add(new SiganlPostModel("CreatedOn", "LESS", getPartOfDate(beforeDate.toInstant().toString())));
        object1.add(new SiganlPostModel("StateCode", "IN", stateCode, true));
        object1.add(new SiganlPostModel("Title", "IN", list, false));
        body.put("FilterItems", object1);

        // objectMapper.writeValueAsString(body) added because request with header MediaType.APPLICATION_JSON returns wrong result
        // AbstractHttpMessageConverter automatically add default content type headers. Quick fix.
        HttpEntity<?> request = new HttpEntity<>(objectMapper.writeValueAsString(body), headers);
        URI uri = UriComponentsBuilder.fromHttpUrl(this.signalUrl)
                .queryParam("offset", 0)
                //.queryParam("rows", 26)
                .queryParam("order", "CreatedOn")
                .queryParam("direction", "desc")
                //.queryParam("filtro", "StateCode=0")
                .buildAndExpand(clientId).toUri();
        Map response = restTemplate.exchange(uri, HttpMethod.POST, request, Map.class).getBody();
        logger.info("Segnalazione data has been obtained.");
        Map<String, Object> responseData = (Map<String, Object>) response.get("response");
        return (List<Object>) responseData.get("Content");
    }

    private List<Object> setTripetForIncidentCheck() {
        List<Object> listaTriplette = new ArrayList<>();
        listaTriplette.add("Informazione -> Commerciale -> Richiesta Ricontatto");
        //listaTriplette.add("Variazione -> Commerciale -> Fatturazione");
        //listaTriplette.add("Variazione -> Tecnico -> MNP IN Post Attivazione");
        return listaTriplette;
    }

    private List<Object> setTripetForSignals() {
        List<Object> listaTriplette = new ArrayList<>();
        listaTriplette.add("Informazione -> Commerciale -> CrossSelling");
        listaTriplette.add("Informazione -> Commerciale -> Richiesta Ricontatto");
        listaTriplette.add("Informazione -> Commerciale -> Invio contratto");
        listaTriplette.add("Informazione -> Amministrativo -> Delibera 252");
        listaTriplette.add("Variazione -> Commerciale -> Cliente cessante");
        listaTriplette.add("Reclamo -> Commerciale -> Contratto");
        listaTriplette.add("Reclamo -> Amministrativo -> Morosità e sospensione");
        listaTriplette.add("Reclamo -> Commerciale -> Mercato");
        listaTriplette.add("Reclamo -> Tecnico -> Misura");
        listaTriplette.add("Reclamo -> Tecnico -> connessione lavori");
        listaTriplette.add("Reclamo -> Amministrativo -> Bonus sociale");
        listaTriplette.add("Reclamo -> Commerciale -> Servizio clienti");
        listaTriplette.add("Reclamo -> Commerciale -> Altro");
        listaTriplette.add("Reclamo -> Commerciale -> Cambio Piano Tariffario");
        listaTriplette.add("Variazione -> Commerciale -> Blocco costo disattivazione");
        listaTriplette.add("Variazione -> Amministrativo -> Annullamento attivazione per timoe");
        listaTriplette.add("Variazione -> Commerciale -> Profilo");
        listaTriplette.add("Variazione -> Commerciale -> Gestione IP Statico");
        listaTriplette.add("Variazione -> Commerciale -> Gestione IP Dinamico");
        listaTriplette.add("Variazione -> Commerciale -> Sconto");
        listaTriplette.add("Variazione -> Commerciale -> agevolazioni sordi e ciechi ");
        listaTriplette.add("Variazione -> Commerciale -> Blocco piano rateizzazione  ");
        listaTriplette.add("Reclamo -> Amministrativo -> Credit policy");
        listaTriplette.add("Variazione -> Amministrativo -> Mancato rinnovo polizza ");
        listaTriplette.add("Variazione -> Tecnico -> Approvazione MNP OUT");
        listaTriplette.add("Variazione -> Tecnico -> Disdetta Servizio Mobile");
        listaTriplette.add("Variazione -> Commerciale -> Reso SIM");
        listaTriplette.add("Variazione -> Tecnico -> Mancata attivazione Mobile");
        listaTriplette.add("Variazione -> Tecnico -> Richiesta Nuova Spedizione SIM");
        listaTriplette.add("Variazione -> Amministrativo -> Emissione Maxirata");
        listaTriplette.add("Variazione -> Amministrativo -> Nota Credito");
        listaTriplette.add("Variazione -> Tecnico -> consumo annuo");
        listaTriplette.add("Reclamo -> Commerciale -> Attivazione contestata");
        listaTriplette.add("Variazione -> Tecnico -> Blocco/Sblocco SIM ");
        listaTriplette.add("Variazione -> Tecnico -> Sostituzione SIM per danneggiamento");
        listaTriplette.add("Variazione -> Tecnico -> Sostituzione SIM per Smarrimento");
        listaTriplette.add("Variazione -> Tecnico -> Sostituzione SIM per furto");
        listaTriplette.add("Variazione -> Tecnico -> Cambio Piano Tariffario ");
        listaTriplette.add("Variazione -> Tecnico -> Cambio Opzione");
        listaTriplette.add("Variazione -> Tecnico -> Barring/Unbarring");
        listaTriplette.add("Variazione -> Tecnico -> Ricarica SIM");
        listaTriplette.add("Variazione -> Tecnico -> MNP IN Post Attivazione");
        listaTriplette.add("Variazione -> Tecnico -> MNP IN Scarto attivazione");
        listaTriplette.add("Reclamo -> Tecnico -> Malfunzionamento");
        listaTriplette.add("Variazione -> Commerciale -> Reso Router");
        listaTriplette.add("Variazione -> Tecnico -> Gestione manuale disattivazione");
        listaTriplette.add("Variazione -> Tecnico -> Disattivazione SIM");
        listaTriplette.add("Variazione -> Tecnico -> Sostituzione SIM per difettosità");
        listaTriplette.add("Variazione -> Commerciale -> Cambio premio ");
        listaTriplette.add("Variazione -> Commerciale -> Spedizione SIM Promozionale");
        listaTriplette.add("Variazione -> Tecnico -> Scarto in attivazione Fibra/Bitstream");
        listaTriplette.add("Variazione -> Amministrativo -> Inserimento Accisa DE");
        listaTriplette.add("Variazione -> Tecnico -> Valutazione cessazione presa");
        listaTriplette.add("Reclamo -> Commerciale -> switching non eseguito");
        listaTriplette.add("Variazione ->  Amministrativo  -> blocco fattura unica ");
        listaTriplette.add("Variazione ->  Amministrativo  -> sblocco fattura unica ");
        listaTriplette.add("Variazione -> Amministrativo -> Blocco Costo Mobile");
        listaTriplette.add("Variazione -> Amministrativo -> Subentro");
        listaTriplette.add("Variazione -> Amministrativo -> Trasloco");
        listaTriplette.add("Variazione -> Amministrativo -> Nota credito");
        listaTriplette.add("Variazione -> Amministrativo -> Nota credito e riemissione fattura");
        listaTriplette.add("Variazione -> Amministrativo -> Nota credito e rimborso");
        listaTriplette.add("Variazione -> Amministrativo -> Blocco fatturazione");
        listaTriplette.add("Variazione -> Tecnico -> Data out manuale");
        listaTriplette.add("Variazione -> Tecnico -> Riattivazione utenze");
        listaTriplette.add("Variazione -> Commerciale -> Registrazione disdetta ADSL");
        listaTriplette.add("Reclamo -> Commerciale -> CORECOM");
        listaTriplette.add("Reclamo -> Commerciale -> Atto giudiziario");
        listaTriplette.add("Variazione -> Commerciale -> Reso device");
        listaTriplette.add("Variazione -> Commerciale -> Cambio contratto device");
        listaTriplette.add("Variazione -> Amministrativo -> Incassi da fax");
        listaTriplette.add("Variazione -> Amministrativo -> Dilazione di pagamento");
        listaTriplette.add("Variazione -> Amministrativo -> Modalità di pagamento in entrata");
        listaTriplette.add("Variazione -> Amministrativo -> Fatturazione");
        listaTriplette.add("Variazione -> Commerciale -> Disdetta Device");
        listaTriplette.add("Variazione -> Amministrativo -> Agevolazione");
        listaTriplette.add("Variazione -> Amministrativo -> Anagrafica");
        listaTriplette.add("Variazione -> Amministrativo -> Diniego RID ");
        listaTriplette.add("Variazione -> Amministrativo -> Fatturazione");
        listaTriplette.add("Variazione -> Amministrativo -> Modalità di Pagamento");
        listaTriplette.add("Variazione -> Amministrativo -> Nota credito");
        listaTriplette.add("Variazione -> Amministrativo -> Nota credito e riemissione fattura");
        listaTriplette.add("Variazione -> Amministrativo -> Nota credito e rimborso");
        listaTriplette.add("Variazione -> Amministrativo -> Rimborso");
        listaTriplette.add("Variazione -> Amministrativo -> Sospensione Blocco Amministrativo");
        listaTriplette.add("Variazione -> Amministrativo -> Subentro  ");
        listaTriplette.add("Variazione -> Amministrativo -> Trasloco");
        listaTriplette.add("Variazione -> Commerciale -> Attivazione nuova utenza");
        listaTriplette.add("Variazione -> Commerciale -> Rimodulazione Offerta");
        listaTriplette.add("Variazione -> Commerciale -> Blocco per sospetta frode");
        listaTriplette.add("Variazione -> Commerciale -> Gestione Opzione");
        listaTriplette.add("Variazione -> Commerciale -> IMEI");
        listaTriplette.add("Variazione -> Commerciale -> MNP IN");
        listaTriplette.add("Variazione -> Commerciale -> Numero in elenco");
        listaTriplette.add("Variazione -> Commerciale -> Nuovo Hardware");
        listaTriplette.add("Variazione -> Commerciale -> Profilo");
        listaTriplette.add("Variazione -> Commerciale -> Prolungamento periodo di prova");
        listaTriplette.add("Variazione -> Commerciale -> Soglie frode");
        listaTriplette.add("Variazione -> Tecnico -> Approvazione traffico HU");
        listaTriplette.add("Variazione -> Tecnico -> Autolettura");
        listaTriplette.add("Variazione -> Tecnico -> Cessazione presa");
        listaTriplette.add("Variazione -> Tecnico -> Riattivazione utenze");
        listaTriplette.add("Variazione -> Tecnico -> Tensione e potenza");
        listaTriplette.add("Reclamo -> Amministrativo -> Applicazione Blocco");
        listaTriplette.add("Reclamo -> Amministrativo -> Doppia fatturazione");
        listaTriplette.add("Reclamo -> Amministrativo -> Fatturazione");
        listaTriplette.add("Reclamo -> Amministrativo -> Sblocco SLA 2 superato  ");
        listaTriplette.add("Reclamo -> Commerciale -> AEEG");
        listaTriplette.add("Reclamo -> Commerciale -> AGCOM");
        listaTriplette.add("Reclamo -> Commerciale -> Area Self-care");
        listaTriplette.add("Reclamo -> Commerciale -> Associazione consumatori");
        listaTriplette.add("Reclamo -> Commerciale -> Attivazione");
        listaTriplette.add("Reclamo -> Commerciale -> Contestazione Legale ");
        listaTriplette.add("Reclamo -> Commerciale -> Dichiarazione di fallimento");
        listaTriplette.add("Reclamo -> Commerciale -> Disattivazione errata ");
        listaTriplette.add("Reclamo -> Commerciale -> Disconoscimento chiamate");
        listaTriplette.add("Reclamo -> Commerciale -> Disconoscimento contratto");
        listaTriplette.add("Reclamo -> Commerciale -> Disdetta non eseguita");
        listaTriplette.add("Reclamo -> Commerciale -> Mancato feedback DOA ");
        listaTriplette.add("Reclamo -> Commerciale -> Penale Precedente Gestore");
        listaTriplette.add("Reclamo -> Commerciale -> Spedizione");
        listaTriplette.add("Reclamo -> Commerciale -> Opzioni non attivate");
        listaTriplette.add("Reclamo -> Tecnico -> Guasto");
        listaTriplette.add("Reclamo -> Tecnico -> Sbalzi di tensione");
        listaTriplette.add("Reclamo -> Tecnico -> Traffico Anomalo");
        listaTriplette.add("Variazione -> Commerciale -> Gestione autoricarica");
        listaTriplette.add("Variazione -> Commerciale -> Cestinamento");
        listaTriplette.add("Variazione -> Commerciale -> Ripristino");
        listaTriplette.add("Variazione -> Commerciale -> Disdetta servizi");
        listaTriplette.add("Reclamo -> Commerciale -> Corpi di polizia");
        listaTriplette.add("Variazione -> Commerciale -> Elenchi telefonici ");
        listaTriplette.add("Variazione -> Commerciale -> Offerta competitor");
        listaTriplette.add("Reclamo -> Commerciale -> conciliazione AEEG");
        listaTriplette.add("Variazione -> Commerciale -> Valutazione cessazione presa");
        listaTriplette.add("Reclamo -> Commerciale -> Truffa consulente");
        listaTriplette.add("Reclamo -> Amministrativo -> Subentro");
        listaTriplette.add("Reclamo -> Commerciale -> Attivazione non richiesta");
        listaTriplette.add("Reclamo -> Amministrativo -> Conto relax");
        listaTriplette.add("Variazione -> Amministrativo -> Codice Ateco");
        listaTriplette.add("Variazione -> Amministrativo -> Sblocco per voltura");
        listaTriplette.add("Variazione -> Tecnico -> Ricostruzione consumi ");
        listaTriplette.add("Reclamo -> Commerciale -> Disconoscimento device");
        listaTriplette.add("Variazione -> Amministrativo -> Sospensione blocco amministrativo per vittime dell'usura");
        listaTriplette.add("Variazione -> Commerciale -> Privacy");
        listaTriplette.add("Variazione -> Commerciale -> Agevolazione");
        listaTriplette.add("Variazione -> Tecnico -> Invio nuova pda mobile");
        listaTriplette.add("Variazione -> Tecnico -> Richiesta riattivazione sim cestinata");
        listaTriplette.add("Variazione -> Tecnico -> Spostamento Gruppo di Misura");
        listaTriplette.add("Variazione -> Amministrativo -> Nota Credito Device");
        listaTriplette.add("Variazione -> Tecnico -> matricola contatore");
        listaTriplette.add("Variazione -> Tecnico -> Invalidazione Autolettura");
        listaTriplette.add("Informazione -> Amministrativo -> Richiesta dilazione di pagamento");
        listaTriplette.add("Variazione -> Commerciale -> Rimodulazione proattiva offerta");
        listaTriplette.add("Variazione -> Commerciale -> Richiesta Disattivazione Amazon Prime");
        listaTriplette.add("Variazione -> Commerciale -> Fatturazione");
        return listaTriplette;
    }

    private String getPartOfDate(String date) {
        return date.substring(0, date.length() - 13) + "23:59:00:00";
    }
}
