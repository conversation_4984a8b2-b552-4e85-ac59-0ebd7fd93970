package com.optimaitalia.service.serviceImpl;

import com.optimaitalia.model.db.EtichettaLabel;
import com.optimaitalia.repository.EtichettaLabelRepository;
import com.optimaitalia.service.EtichettaLabelService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Transactional
public class EtichettaLabelServiceImpl implements EtichettaLabelService {

    private static final Logger logger = LogManager.getLogger(EtichettaLabelServiceImpl.class);

    private final EtichettaLabelRepository labelRepository;

    @Autowired
    public EtichettaLabelServiceImpl(EtichettaLabelRepository labelRepository) {
        this.labelRepository = labelRepository;
    }

    @Override
    public EtichettaLabel getEtichettaLabelByPodUtNumber(String pod) {
        logger.info("Getting the EtichettaLabel by PodUtNumber: " + pod);
        return labelRepository.getEtichettaLabelByPodUtNumber(pod);
    }

    @Override
    public EtichettaLabel createOrUpdateEtichettaLabel(EtichettaLabel etichettaLabel) {
        logger.info("CreateOrUpdate EtichettaLabel for user with id: " + etichettaLabel.getClientId());
        if (etichettaLabel.getDescription().isEmpty()) {
            labelRepository.deleteById(etichettaLabel.getId());
            return etichettaLabel;
        } else return labelRepository.save(etichettaLabel);
    }
}
