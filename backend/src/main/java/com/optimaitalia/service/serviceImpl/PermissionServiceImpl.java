package com.optimaitalia.service.serviceImpl;

import com.optimaitalia.service.MobileService;
import com.optimaitalia.service.PermissionService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class PermissionServiceImpl implements PermissionService {

    private final MobileService mobileService;

    public PermissionServiceImpl(MobileService mobileService) {
        this.mobileService = mobileService;
    }

    @Override
    public boolean mobileNumberBelongToClient(Long simNumber, String clientId) {
        if (simNumber != null && !StringUtils.isEmpty(clientId)) {
            return !mobileService.findMobileContracts(Long.valueOf(clientId), simNumber).isEmpty();
        }
        return false;
    }
}
