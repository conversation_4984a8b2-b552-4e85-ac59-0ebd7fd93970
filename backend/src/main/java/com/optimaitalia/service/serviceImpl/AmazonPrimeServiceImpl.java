package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optimaitalia.model.amazonprime.AmazonPrime;
import com.optimaitalia.service.AmazonPrimeService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

@Service
public class AmazonPrimeServiceImpl implements AmazonPrimeService {

    private final RestTemplate restTemplate;

    @Value("${restdata.urls.amazon-prime-data}")
    private String amazonPrimeDataUrl;

    @Value("${restdata.urls.amazon-prime-unsubscribe-url}")
    private String unsubscribeAmazonPrimeUrl;


    public AmazonPrimeServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public AmazonPrime getAmazonPrimeData(Long clientId) throws IOException {
        return restTemplate.getForObject(amazonPrimeDataUrl, AmazonPrime.class, clientId);
    }

    @Override
    public AmazonPrime unsubscribe(Long clientId) {
        return restTemplate.exchange(this.unsubscribeAmazonPrimeUrl, HttpMethod.GET, null, AmazonPrime.class, clientId).getBody();
    }

}
