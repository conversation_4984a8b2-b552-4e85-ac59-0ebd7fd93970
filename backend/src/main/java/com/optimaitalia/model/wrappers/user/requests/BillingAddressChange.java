package com.optimaitalia.model.wrappers.user.requests;

import java.util.Date;
import java.util.List;

public class BillingAddressChange extends Change {

    private String companyName;

    private String fiscalCode;

    private String VATNumber;

    private String phoneNumber;

    private String email;

    private Date startingDate;

    private List<String> listaPod;

    private String name;

    private String cognome;

    private String toponimo;

    private String via;

    private String civico;

    private String cap;

    private String comune;

    private String idFatt;

    private String idSin;

    private String idCrm;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getFiscalCode() {
        return fiscalCode;
    }

    public void setFiscalCode(String fiscalCode) {
        this.fiscalCode = fiscalCode;
    }

    public String getVATNumber() {
        return VATNumber;
    }

    public void setVATNumber(String VATNumber) {
        this.VATNumber = VATNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getStartingDate() {
        return startingDate;
    }

    public void setStartingDate(Date startingDate) {
        this.startingDate = startingDate;
    }

    public List<String> getListaPod() {
        return listaPod;
    }

    public void setListaPod(List<String> listaPod) {
        this.listaPod = listaPod;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCognome() {
        return cognome;
    }

    public void setCognome(String cognome) {
        this.cognome = cognome;
    }

    public String getToponimo() {
        return toponimo;
    }

    public void setToponimo(String toponimo) {
        this.toponimo = toponimo;
    }

    public String getVia() {
        return via;
    }

    public void setVia(String via) {
        this.via = via;
    }

    public String getCivico() {
        return civico;
    }

    public void setCivico(String civico) {
        this.civico = civico;
    }

    public String getCap() {
        return cap;
    }

    public void setCap(String cap) {
        this.cap = cap;
    }

    public String getComune() {
        return comune;
    }

    public void setComune(String comune) {
        this.comune = comune;
    }

    public String getIdFatt() {
        return idFatt;
    }

    public void setIdFatt(String idFatt) {
        this.idFatt = idFatt;
    }

    public String getIdSin() {
        return idSin;
    }

    public void setIdSin(String idSin) {
        this.idSin = idSin;
    }

    public String getIdCrm() {
        return idCrm;
    }

    public void setIdCrm(String idCrm) {
        this.idCrm = idCrm;
    }
}
