package com.optimaitalia.model.wrappers.mobile.conracts;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.util.Date;

public class Sim {

    private Long id;
    @JsonIgnore
    private Long imsi;
    @JsonIgnore
    private Date expirationDate;
    @JsonIgnore
    private Integer pin1;
    @JsonIgnore
    private Integer pin2;

    private Integer puk1;
    @JsonIgnore
    private Integer puk2;
    @JsonIgnore
    private String simStsCh;
    @JsonIgnore
    private Integer simStsChId;
    @JsonIgnore
    private Integer simSubRangeId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getImsi() {
        return imsi;
    }

    public void setImsi(Long imsi) {
        this.imsi = imsi;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Integer getPin1() {
        return pin1;
    }

    public void setPin1(Integer pin1) {
        this.pin1 = pin1;
    }

    public Integer getPin2() {
        return pin2;
    }

    public void setPin2(Integer pin2) {
        this.pin2 = pin2;
    }

    public Integer getPuk1() {
        return puk1;
    }

    public void setPuk1(Integer puk1) {
        this.puk1 = puk1;
    }

    public Integer getPuk2() {
        return puk2;
    }

    public void setPuk2(Integer puk2) {
        this.puk2 = puk2;
    }

    public String getSimStsCh() {
        return simStsCh;
    }

    public void setSimStsCh(String simStsCh) {
        this.simStsCh = simStsCh;
    }

    public Integer getSimStsChId() {
        return simStsChId;
    }

    public void setSimStsChId(Integer simStsChId) {
        this.simStsChId = simStsChId;
    }

    public Integer getSimSubRangeId() {
        return simSubRangeId;
    }

    public void setSimSubRangeId(Integer simSubRangeId) {
        this.simSubRangeId = simSubRangeId;
    }
}
