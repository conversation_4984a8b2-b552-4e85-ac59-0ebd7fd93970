package com.optimaitalia.model.wrappers.communication;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.CommunicationNoteDataDeserializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CommunicationNote {
    private String tripletta;
    private String soggettoNota;
    private String testoNota;
    @JsonDeserialize(using = CommunicationNoteDataDeserializer.class)
    private Date dataNota;
    private List<Allegati> allegati;
}
