package com.optimaitalia.model.wrappers.payment;

public class Payment {

    private Long clientId;

    private StringBuilder invoiceNumberStr = new StringBuilder();
    private StringBuilder invoiceAmountStr = new StringBuilder();
    private StringBuilder invoiceDateStr = new StringBuilder();
    private StringBuilder invoiceExternalIdsStr = new StringBuilder();

    public StringBuilder getInvoiceNumberStr() {
        return invoiceNumberStr;
    }

    public StringBuilder getInvoiceAmountStr() {
        return invoiceAmountStr;
    }

    public StringBuilder getInvoiceDateStr() {
        return invoiceDateStr;
    }

    public StringBuilder getInvoiceExternalIdsStr() {
        return invoiceExternalIdsStr;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }
}
