package com.optimaitalia.model.wrappers.user.requests;

public class ChangeRequest {

    private String customerId;

    private String user;

    private String userFullName;

    private String incidentId;

    private String changeId;

    private String parentChangeId;

    private Change change;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getUserFullName() {
        return userFullName;
    }

    public void setUserFullName(String userFullName) {
        this.userFullName = userFullName;
    }

    public String getIncidentId() {
        return incidentId;
    }

    public void setIncidentId(String incidentId) {
        this.incidentId = incidentId;
    }

    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    public String getParentChangeId() {
        return parentChangeId;
    }

    public void setParentChangeId(String parentChangeId) {
        this.parentChangeId = parentChangeId;
    }

    public Change getChange() {
        return change;
    }

    public void setChange(Change change) {
        this.change = change;
    }


    @Override
    public String toString() {
        return "ChangeRequest{" +
                "customerId='" + customerId + '\'' +
                ", user='" + user + '\'' +
                ", userFullName='" + userFullName + '\'' +
                ", incidentId='" + incidentId + '\'' +
                ", changeId='" + changeId + '\'' +
                ", parentChangeId='" + parentChangeId + '\'' +
                ", change=" + change +
                '}';
    }
}
