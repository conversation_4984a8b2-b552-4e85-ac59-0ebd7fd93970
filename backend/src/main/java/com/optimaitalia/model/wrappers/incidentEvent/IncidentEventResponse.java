package com.optimaitalia.model.wrappers.incidentEvent;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class IncidentEventResponse {

    private String status;

    private String ticketNumber;

    private String incidentId;

    private String message;

    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    @JsonProperty("Esito")
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("ticketNumber")
    public String getTicketNumber() {
        return ticketNumber;
    }

    @JsonProperty("TicketNumber")
    public void setTicketNumber(String ticketNumber) {
        this.ticketNumber = ticketNumber;
    }

    @JsonProperty("incidentId")
    public String getIncidentId() {
        return incidentId;
    }

    @JsonProperty("IncidentId")
    public void setIncidentId(String incidentId) {
        this.incidentId = incidentId;
    }

    @JsonProperty("message")
    public String getMessage() {
        return message;
    }

    @JsonProperty("Message")
    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IncidentEventResponse that = (IncidentEventResponse) o;
        return Objects.equals(status, that.status) &&
                Objects.equals(ticketNumber, that.ticketNumber) &&
                Objects.equals(message, that.message) &&
                Objects.equals(incidentId, that.incidentId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(status, ticketNumber, message, incidentId);
    }

    @Override
    public String toString() {
        return "IncidentEventResponse{" +
                "status='" + status + '\'' +
                ", ticketNumber='" + ticketNumber + '\'' +
                ", message='" + message + '\'' +
                ", incidentId='" + incidentId + '\'' +
                '}';
    }
}
