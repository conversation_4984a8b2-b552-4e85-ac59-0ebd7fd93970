package com.optimaitalia.model.wrappers.mobile;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ActivateOptionResponse {

    private Integer code;

    private String errorMessage;

    private Long operationId;

    @JsonProperty("code")
    public Integer getCode() {
        return code;
    }

    @JsonProperty("codice")
    public void setCode(Integer code) {
        this.code = code;
    }

    @JsonProperty("errorMessage")
    public String getErrorMessage() {
        return errorMessage;
    }

    @JsonProperty("errore")
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @JsonProperty("operationId")
    public Long getOperationId() {
        return operationId;
    }

    @JsonProperty("idOperazione")
    public void setOperationId(Long operationId) {
        this.operationId = operationId;
    }

    @Override
    public String toString() {
        return "ActivateOptionResponse{" +
                "code=" + code +
                ", errorMessage='" + errorMessage + '\'' +
                ", operationId=" + operationId +
                '}';
    }
}
