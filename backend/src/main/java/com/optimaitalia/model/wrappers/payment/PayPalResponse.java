package com.optimaitalia.model.wrappers.payment;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PayPalResponse {
    private Integer idResponse;
    private Integer esito;
    private String descrizione;
    private String dettaglio;
    private String returnURL;
    private String typePayment;
    private Integer clientId;
    private Integer idTransaction;

    @JsonProperty("IdResponse")
    public Integer getIdResponse() {
        return idResponse;
    }

    @JsonProperty("IdResponse")
    public void setIdResponse(Integer idResponse) {
        this.idResponse = idResponse;
    }

    @JsonProperty("esito")
    public Integer getEsito() {
        return esito;
    }

    @JsonProperty("Esito")
    public void setEsito(Integer esito) {
        this.esito = esito;
    }

    @JsonProperty("Descrizione")
    public String getDescrizione() {
        return descrizione;
    }

    @JsonProperty("descrizione")
    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    @JsonProperty("dettaglio")
    public String getDettaglio() {
        return dettaglio;
    }

    @JsonProperty("Dettaglio")
    public void setDettaglio(String dettaglio) {
        this.dettaglio = dettaglio;
    }

    @JsonProperty("returnURL")
    public String getReturnURL() {
        return returnURL;
    }

    @JsonProperty("Return_URL")
    public void setReturnURL(String returnURL) {
        this.returnURL = returnURL;
    }

    @JsonProperty("typePayment")
    public String getTypePayment() {
        return typePayment;
    }

    @JsonProperty("TipoPagamento")
    public void setTypePayment(String typePayment) {
        this.typePayment = typePayment;
    }

    @JsonProperty("clientId")
    public Integer getClientId() {
        return clientId;
    }

    @JsonProperty("CodiceCliente")
    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }

    @JsonProperty("idTransaction")
    public Integer getIdTransaction() {
        return idTransaction;
    }

    @JsonProperty("IdTransazione")
    public void setIdTransaction(Integer idTransaction) {
        this.idTransaction = idTransaction;
    }
}
