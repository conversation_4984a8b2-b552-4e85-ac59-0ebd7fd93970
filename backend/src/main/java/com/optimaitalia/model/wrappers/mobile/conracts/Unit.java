package com.optimaitalia.model.wrappers.mobile.conracts;

public class Unit {

    private Integer id;

    private Integer counterTypeId;

    private String displayName;

    private String parentId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCounterTypeId() {
        return counterTypeId;
    }

    public void setCounterTypeId(Integer counterTypeId) {
        this.counterTypeId = counterTypeId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
}
