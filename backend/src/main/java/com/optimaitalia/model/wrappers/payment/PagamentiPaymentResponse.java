package com.optimaitalia.model.wrappers.payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PagamentiPaymentResponse {

    @JsonProperty("Esito")
    private String Esito;

    @JsonProperty("CodiceEsito")
    private String CodiceEsito;

    @JsonProperty("Descrizione")
    private String Descrizione;

    @JsonProperty("IdRisp")
    private Integer IdRisp;

    @JsonProperty("IdCliente")
    private Long IdCliente;

    @JsonProperty("PaymentId")
    private String PaymentId;

    @JsonProperty("IdOrdine")
    private String IdOrdine;

    @JsonProperty("APILicenseKeyEasy")
    private String APILicenseKeyEasy;

    @JsonProperty("SDK")
    private String SDK;

}
