package com.optima.security.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.exceptions.ProfileException;
import com.optima.security.model.ApplicationUser;
import com.optima.security.service.impl.TokenAuthenticationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

public class JWTAdminLoginFilter extends AbstractAuthenticationProcessingFilter {

    private static final Logger logger = LogManager.getLogger(JWTLoginFilter.class);

    private final Environment environment;
    @Autowired
    private TokenAuthenticationService tokenAuthenticationService;

    public JWTAdminLoginFilter(String url, AuthenticationManager authManager, Environment environment) {
        super(new AntPathRequestMatcher(url));
        this.environment = environment;
        setAuthenticationManager(authManager);
    }

    @Override
    public Authentication attemptAuthentication(
            HttpServletRequest req, HttpServletResponse res)
            throws AuthenticationException, IOException {
        if (!environment.acceptsProfiles("preprod") &&  !environment.acceptsProfiles("dev")) {
            throw new ProfileException("Active profile isn't a preprod");
        }
        ApplicationUser creds = new ObjectMapper()
                .readValue(req.getInputStream(), ApplicationUser.class);
        logger.info("{}: Api call: /AdminLoginJWT", creds.getUsername());
        Set<GrantedAuthority> grantedAuthorities = new HashSet<>();
        grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
        req.setAttribute("userId", creds.getUserId());
        return getAuthenticationManager().authenticate(
                new UsernamePasswordAuthenticationToken(
                        creds.getUsername(),
                        creds.getPassword(),
                        grantedAuthorities
                )
        );
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain,

                                            Authentication auth) throws IOException {
        try {
            tokenAuthenticationService.addAuthentication(res, auth, req.getAttribute("userId").toString());
        } catch (JwtTokenException ignored) {

        }

    }


}
