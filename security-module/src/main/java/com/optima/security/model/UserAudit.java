package com.optima.security.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.sf.oval.constraint.NotNull;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "users_audit")
public class UserAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @NotNull
    @Column(name = "client_id", nullable = false)
    private String clientId;

    @NotNull
    @Column(name = "email", nullable = false)
    private String email;

    @NotNull
    @Column(name = "date_access", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateAccess;

    @NotNull
    @Column(name = "is_social_login", nullable = false)
    private Boolean isSocialLogin;

    @NotNull
    @Column(name = "is_app_login", nullable = false)
    private Boolean isAppLogin;

    @NotNull
    @Column(name = "app_name")
    private String appName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserAudit)) return false;
        UserAudit userAudit = (UserAudit) o;
        return getId().equals(userAudit.getId()) && getClientId().equals(userAudit.getClientId()) && getEmail().equals(userAudit.getEmail())
                && getDateAccess().equals(userAudit.getDateAccess()) && getIsSocialLogin().equals(userAudit.getIsSocialLogin())
                && getIsAppLogin().equals(userAudit.getIsAppLogin()) && getAppName().equals(userAudit.getAppName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getClientId(), getEmail(), getDateAccess(), getIsSocialLogin(), getIsAppLogin(), getAppName());
    }

    public UserAudit(String clientId, String email, Date dateAccess, Boolean isSocialLogin, Boolean isAppLogin, String appName) {
        this.clientId = clientId;
        this.email = email;
        this.dateAccess = dateAccess;
        this.isSocialLogin = isSocialLogin;
        this.isAppLogin = isAppLogin;
        this.appName = appName;
    }
}
