create table tariff_data
(
  zone                        varchar(255) not null
    primary key,
  sms                         decimal(9, 2),
  mms                         decimal(9, 2),
  call_connection             decimal(9, 2),
  call_min                    decimal(9, 2),
  videocall_connection        decimal(9, 2),
  videocall_min               decimal(9, 2),
  data_mb                     decimal(9, 2),
  roaming_call_connection     decimal(9, 2),
  zona_a_roaming_call         decimal(9, 2),
  zona_b_roaming_call         decimal(9, 2),
  zona_c_roaming_call         decimal(9, 2),
  zona_d_roaming_call         decimal(9, 2),
  roaming_sms                 decimal(9, 2),
  roaming_mms                 decimal(9, 2),
  call_from_abroad_connection decimal(9, 2),
  call_from_abroad_min        decimal(9, 2),
  sms_from_abroad             decimal(9, 2),
  mms_from_abroad             decimal(9, 2)
)
  go

INSERT INTO tariff_data (zone, sms, mms, call_connection, call_min, videocall_connection, videocall_min, data_mb, roaming_call_connection, zona_a_roaming_call, zona_b_roaming_call, zona_c_roaming_call, zona_d_roaming_call, roaming_sms, roaming_mms, call_from_abroad_connection, call_from_abroad_min, sms_from_abroad, mms_from_abroad) VALUES ('Zone 1', 0.07, 1.50, 0.00, 0.20, 0.15, 1.50, null, null, null, null, null, null, null, null, null, null, null, null);
INSERT INTO tariff_data (zone, sms, mms, call_connection, call_min, videocall_connection, videocall_min, data_mb, roaming_call_connection, zona_a_roaming_call, zona_b_roaming_call, zona_c_roaming_call, zona_d_roaming_call, roaming_sms, roaming_mms, call_from_abroad_connection, call_from_abroad_min, sms_from_abroad, mms_from_abroad) VALUES ('Zone 2', 0.30, 1.70, 0.16, 0.90, 0.15, 1.50, null, null, null, null, null, null, null, null, null, null, null, null);
INSERT INTO tariff_data (zone, sms, mms, call_connection, call_min, videocall_connection, videocall_min, data_mb, roaming_call_connection, zona_a_roaming_call, zona_b_roaming_call, zona_c_roaming_call, zona_d_roaming_call, roaming_sms, roaming_mms, call_from_abroad_connection, call_from_abroad_min, sms_from_abroad, mms_from_abroad) VALUES ('Zone 3', 0.30, 2.50, 0.16, 3.00, 0.15, 5.50, null, null, null, null, null, null, null, null, null, null, null, null);
INSERT INTO tariff_data (zone, sms, mms, call_connection, call_min, videocall_connection, videocall_min, data_mb, roaming_call_connection, zona_a_roaming_call, zona_b_roaming_call, zona_c_roaming_call, zona_d_roaming_call, roaming_sms, roaming_mms, call_from_abroad_connection, call_from_abroad_min, sms_from_abroad, mms_from_abroad) VALUES ('Zone A', null, null, null, null, null, null, null, null, 0.20, 0.23, 0.23, 3.00, 0.07, 1.30, null, null, null, null);
INSERT INTO tariff_data (zone, sms, mms, call_connection, call_min, videocall_connection, videocall_min, data_mb, roaming_call_connection, zona_a_roaming_call, zona_b_roaming_call, zona_c_roaming_call, zona_d_roaming_call, roaming_sms, roaming_mms, call_from_abroad_connection, call_from_abroad_min, sms_from_abroad, mms_from_abroad) VALUES ('Zone B', null, null, null, null, null, null, 0.24, null, 0.23, 0.23, 0.23, 3.00, 0.15, 1.70, null, 0.06, null, null);
INSERT INTO tariff_data (zone, sms, mms, call_connection, call_min, videocall_connection, videocall_min, data_mb, roaming_call_connection, zona_a_roaming_call, zona_b_roaming_call, zona_c_roaming_call, zona_d_roaming_call, roaming_sms, roaming_mms, call_from_abroad_connection, call_from_abroad_min, sms_from_abroad, mms_from_abroad) VALUES ('Zone C', null, null, null, null, null, null, null, null, 0.15, 0.23, 0.23, 3.00, 0.10, 1.30, null, null, null, null);
INSERT INTO tariff_data (zone, sms, mms, call_connection, call_min, videocall_connection, videocall_min, data_mb, roaming_call_connection, zona_a_roaming_call, zona_b_roaming_call, zona_c_roaming_call, zona_d_roaming_call, roaming_sms, roaming_mms, call_from_abroad_connection, call_from_abroad_min, sms_from_abroad, mms_from_abroad) VALUES ('Zone D', null, null, null, null, null, null, 0.24, 3.00, 3.00, 3.00, 3.00, 3.00, 0.80, 2.50, 1.50, 1.50, null, null);

create table roaming_to_italia
(
  id           int identity
  primary key,
  country_name varchar(255),
  region       varchar(255),
  zone         varchar(255)
  references tariff_data,
  coverage     varchar(255)
)
go

INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Austria', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Belgium', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Bulgaria', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Croatia', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Cyprus', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Denmark', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Estonia', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Finland', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('France', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Germany', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Greece', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Ireland', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Italy', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Latvia', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Lithuania', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Luxembourg', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Malta', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Netherlands', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Poland', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Portugal', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Czech Republic', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Romania', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Slovakia', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Slovenia', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Spain', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Sweden', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('United Kingdom of Great Britain and Northern Ireland', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Hungary', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Norway', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Liechtenstein', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Iceland', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('San Marino', 'Europa', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Åland Islands', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Martinique', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Guadeloupe', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('French Guiana', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Réunion', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Mayotte', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Azzorre', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Madeira', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Isole Canarie', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Ceuta', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Melilla', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Gibraltar', 'Territori specifici', 'Zone A', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Isle of Man', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('New Caledonia', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('French Polynesia', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Wallis and Futuna', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('French Southern Territories', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Saint Pierre and Miquelon', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Saint Barthélemy', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Isole Clipperton', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Antille', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Aruba', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Anguilla', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Bermuda', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Territori Antartici Inglesi', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('British Indian Ocean Territory', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Virgin Islands (British)', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Cayman Islands', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Falkland Islands (Malvinas)', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Montserrat', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Pitcairn', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Saint Helena, Ascension and Tristan da Cunha', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('South Georgia and the South Sandwich Islands', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Turks and Caicos Islands', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Akrotiri and Dhekelia', 'Extra Europa & Altre', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Faroe Islands', 'Territori specifici', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Isole Sparsedell’Oceano Indiano', 'Territori specifici', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Jersey', 'Territori specifici', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Guernsey', 'Territori specifici', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Isle of Man', 'Territori specifici', 'Zone B', 'Non coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Andorra', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Switzerland', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Serbia', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Monaco', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Albania', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Turkey', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Bosnia and Herzegovina', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Macedonia (the former Yugoslav Republic of)', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Montenegro', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Belarus', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Moldova (Republic of)', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Ukraine', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Republic of Kosovo', 'Extra Europa UE', 'Zone B', 'Coperte');
INSERT INTO roaming_to_italia (country_name, region, zone, coverage) VALUES ('Resto del mondo', 'Resto del mondo', 'Zone D', null);

create table italia_vs_estero
(
  id           int identity
  primary key,
  country_name varchar(255),
  region       varchar(255),
  zone         varchar(255)
    references tariff_data
)
  go

INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Austria', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Belgium', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Bulgaria', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Cyprus', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Denmark', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Estonia', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Finland', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('France', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Germany', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('United Kingdom of Great Britain and Northern Ireland', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Greece', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Ireland', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Italy', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Latvia', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Lithuania', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Luxembourg', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Malta', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Netherlands', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Poland', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Portugal', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Czech Republic', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Romania', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Slovakia', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Slovenia', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Spain', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Sweden', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Hungary', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Croatia', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Iugoslavia', 'Europa', 'Zone 1');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Andorra', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Azzorre', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Gibraltar', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Guadeloupe', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Guernsey', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('French Guiana', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Iceland', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Isle of Man', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Isole Canarie', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Faroe Islands', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Jersey', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Liechtenstein', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Madeira', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Martinique', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Norway', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Réunion', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('San Marino', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Switzerland', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('United States of America', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Canada', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Isole Caraibiche (prefisso 001)', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Kazakhstan', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Tajikistan', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Uzbekistan', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Serbia', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Belarus', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Bosnia and Herzegovina', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Faroe Islands', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Russian Federation', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Macedonia (the former Yugoslav Republic of)', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Moldova (Republic of)', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Ukraine', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Alaska', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Hawaii', 'Extra Europa & Altre', 'Zone 2');
INSERT INTO italia_vs_estero (country_name, region, zone) VALUES ('Resto del mondo', 'Resto del mondo', 'Zone 3');

