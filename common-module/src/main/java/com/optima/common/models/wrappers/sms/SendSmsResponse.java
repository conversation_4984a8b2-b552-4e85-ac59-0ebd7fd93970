package com.optima.common.models.wrappers.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.optima.common.models.wrappers.ResponseResult;

public class SendSmsResponse {

    private ResponseResult result;

    private Integer transmissionCode;

    @JsonProperty("result")
    public ResponseResult getResult() {
        return result;
    }

    @JsonProperty("esito")
    public void setResult(ResponseResult result) {
        this.result = result;
    }

    public Integer getTransmissionCode() {
        return transmissionCode;
    }

    public void setTransmissionCode(Integer transmissionCode) {
        this.transmissionCode = transmissionCode;
    }
}
