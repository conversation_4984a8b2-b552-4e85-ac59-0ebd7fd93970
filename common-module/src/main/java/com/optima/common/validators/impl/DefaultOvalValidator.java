package com.optima.common.validators.impl;

import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import net.sf.oval.ConstraintViolation;
import net.sf.oval.Validator;
import net.sf.oval.context.FieldContext;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 27.04.17.
 */
public class DefaultOvalValidator implements OvalValidator {

    private final Validator validator;

    public DefaultOvalValidator(Validator validator) {
        this.validator = validator;
    }

    @Override
    public void validate(Object object) throws ValidateException {
        List<ConstraintViolation> list = validator.validate(object);
        Map<String, String> errors = new HashMap<>();
        if (list.size() > 0) {
            for (ConstraintViolation constraintViolation : list) {
                String fieldName = ((FieldContext) (constraintViolation.getContext())).getField().getName();
                errors.put(fieldName, constraintViolation.getMessage());
            }
            throw new ValidateException(errors);
        }
    }
}
