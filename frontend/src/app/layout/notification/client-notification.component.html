<div class="client-notification">
  <!-- Notification Bell Icon -->
  <div class="notification-bell-container" (click)="toggleNotificationPanel()">
    <img *ngIf="clientNotificationList.length === 0" src="/assets/img/icons/notification_bell_off.png"
         class="notification-bell" alt="bell"/>
    <img *ngIf="unReadNotificationList.length === 0 && clientNotificationList.length > 0"
         class="notification-bell bell-on"
         src="/assets/img/icons/notification_bell_on.png" alt="bell">
    <div *ngIf="unReadNotificationList.length > 0">
      <img class="notification-bell" src="/assets/img/icons/notification_bell_active.png" alt="bell">
      <div class="badge">{{ unReadNotificationList.length }}</div>
    </div>
  </div>
  <!-- Right-Side Panel -->
  <div class="notification-panel" [class.open]="isPanelOpen">
    <div class="panel-header">
      <div class="header">LE TUE NOTIFICHE</div>
      <i class="fa fa-times" aria-hidden="true" (click)="toggleNotificationPanel()"></i>
    </div>
    <div class="panel-content">
      <!-- No notifications -->
      <div *ngIf="clientNotificationList.length === 0" class="no-notifications">
        Non sono presenti notifiche
      </div>
      <!-- Notifications exist -->
      <ng-container *ngFor="let notification of clientNotificationList; trackBy: trackByNotificationId">
        <mat-option [ngClass]="{'no-border': expandedNotificationId === notification.id}"
                    (click)="openNotification(notification)">
          <div class="title-notification">
            <mat-icon *ngIf="!notification.readDate" class="notification-circle">lens</mat-icon>
            <div [ngClass]="{'font-weight-bold': !notification.readDate}">
              {{ notification.title }}
            </div>
            <img src="assets/img/icons/arrow-down_16x16.png" alt="arrow"
                 [class.rotated]="expandedNotificationId === notification.id"/>
          </div>
          <div [ngClass]="{'font-weight-bold': !notification.readDate}">
            {{ notification.insertDate | date : 'dd/MM/yyyy \'alle\' HH:mm' }}
          </div>
        </mat-option>
        <div *ngIf="expandedNotificationId === notification.id"
             class="message expanded">
          <ng-container *ngIf="notification.firstPartOfMessage || notification.secondPartOfMessage; else fullMessage">
            <span *ngIf="notification.firstPartOfMessage">{{ notification.firstPartOfMessage }}</span>
            <a *ngIf="notification.textMarker && notification.redirectUrl"
               [attr.href]="'//' + notification.redirectUrl" target="_blank">
              <u>{{ notification.textMarker }}</u>
            </a>
            <span *ngIf="notification.secondPartOfMessage">{{ notification.secondPartOfMessage }}</span>
          </ng-container>
          <ng-template #fullMessage>{{ notification.message }}</ng-template>
        </div>
      </ng-container>
    </div>
  </div>
</div>
<div class="modal-div show" *ngIf="showModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideWindow()"></i>
    <div class="modal-header-info">
      <div>{{ unReadNotificationList[0].title }}</div>
      <div>{{ unReadNotificationList[0].insertDate | date : 'dd/MM/yyyy \'alle\' HH:mm' }}</div>
    </div>
    <div class="modal-text">
      <ng-container
        *ngIf="unReadNotificationList[0].firstPartOfMessage || unReadNotificationList[0].secondPartOfMessage; else fullMessage">
        <span
          *ngIf="unReadNotificationList[0].firstPartOfMessage">{{ unReadNotificationList[0].firstPartOfMessage }}</span>
        <a *ngIf="unReadNotificationList[0].textMarker && unReadNotificationList[0].redirectUrl"
           [attr.href]="'//' + unReadNotificationList[0].redirectUrl" target="_blank">
          <u>{{ unReadNotificationList[0].textMarker }}</u>
        </a>
        <span
          *ngIf="unReadNotificationList[0].secondPartOfMessage">{{ unReadNotificationList[0].secondPartOfMessage }}</span>
      </ng-container>
      <ng-template #fullMessage>{{ unReadNotificationList[0].message }}</ng-template>
    </div>
  </div>
</div>
