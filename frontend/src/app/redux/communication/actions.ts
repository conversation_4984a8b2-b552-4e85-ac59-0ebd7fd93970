import { Injectable } from '@angular/core';
import { dispatch, select } from '@angular-redux/store';
import { Type } from './types';
import CommunicationState from '../model/CommunicationState';
import { Observable } from 'rxjs/Observable';


@Injectable()
export class CommunicationActions {

  @select(['communication'])
  private communicationState: Observable<CommunicationState>;

  communication: CommunicationState;

  constructor() {
    this.communicationState.subscribe(state => {
      this.communication = state;
    });
  }

  @dispatch()
  loadCommunicationEmailInfo(clientId: string) {
    return {type: Type.COMMUNICATION_EMAIL_LOADING, clientId};
  }

  @dispatch()
  loadRecommendedBlocks(clientId: string) {
    return {type: Type.RECOMMENDED_BLOCK_LOADING, clientId};
  }

  @dispatch()
  loadCommunicationNotes(clientId: string) {
    return {type: Type.COMMUNICATION_NOTE_LOADING, clientId};
  }


  loadCommunicationEmailInfoIfNotExist(clientId: string) {
    const {communicationsEmailsLoading, communicationsEmailsLoaded} = this.communication;
    if (!communicationsEmailsLoading && !communicationsEmailsLoaded && clientId) {
      this.loadCommunicationEmailInfo(clientId);
    }
  }

  loadRecommendedBlocksIfNotExist(clientId: string) {
    const {recommendedBlocksLoading, recommendedBlocksLoaded} = this.communication;
    if (!recommendedBlocksLoading && !recommendedBlocksLoaded && clientId) {
      this.loadRecommendedBlocks(clientId);
    }
  }

  loadCommunicationNotesIfNotExist(clientId: string) {
    const {communicationNoteLoaded, communicationNoteLoading} = this.communication;
    if (!communicationNoteLoading && !communicationNoteLoaded && clientId) {
      this.loadCommunicationNotes(clientId);
    }
  }

  loadCommunicationDataIfNotExist(clientId: string) {
    this.loadCommunicationEmailInfoIfNotExist(clientId);
    this.loadRecommendedBlocksIfNotExist(clientId);
    this.loadCommunicationNotesIfNotExist(clientId);
  }

}
