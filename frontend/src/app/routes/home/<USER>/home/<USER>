<div class="container-fluid">
  <div class="row">
    <div class="block-menu clearfix">
      <div class="col-xl-3 col-lg-3 col-md-3 leftMenu forDesktop">
        <app-active-services></app-active-services>
      </div>
      <div *ngIf="isMobile && !isAmazonPrimeLayoutActive" class="col-xl-3 col-lg-3 col-md-3 leftMenu clearfix forMobile">
        <app-active-services-mobile-v></app-active-services-mobile-v>
      </div>
      <div class="col-xl-9 col-lg-9 col-md-9 total">
        <router-outlet></router-outlet>
        <button *ngIf="isUserCondominio()" class="btn-condomini" (click)="navigate()">
          <img class="btn-condomini-img" src="assets/img/icons/arrow-down_16x16.png" alt="arrow"/>
          I tuoi Condomini
        </button>
      </div>
    </div>
  </div>
</div>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
