.titleBlock {
  clear: both;
  text-align: center;
  .title {
    color: #36749d;
  }
  .icon {
    position: absolute;
    background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_SIM.png") no-repeat;
    width: 80px;
    background-size: contain;
    top: -10px;
    left: 10px;

  }
  margin-bottom: 20px;
  border: 2px solid #b6cce3;
  border-radius: 5px;
  padding: 15px;
}

.row {
  display: block;
  flex: 0 1 auto !important;
}

.titleBlock {
  clear: both;
  text-align: center;
  .title {
    color: #36749d;
  }
  .icon {
    position: absolute;
    background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_SIM.png") no-repeat;
    width: 80px;
    background-size: contain;
    top: -10px;
    left: 10px;

  }
  margin-bottom: 20px;
  border: 2px solid #b6cce3;
  border-radius: 5px;
  padding: 15px;
}

.main {
  padding-top: 30px;
}

.record-row {
  border-bottom: 1px solid #b6cce3;
}

.no-padding {
  padding-right: 0 !important;
}

.sim-details {
  color: #36749d;
  //min-height: 350px;
  border: 1px solid #b6cce3;
  border-radius: 5px;
  padding: 15px;
  background-color: white;
  //height: 40vh;
  height: auto;
  h2 {
    color: #e54c36;
    font-weight: bold;
    padding: 15px 0;
  }
  .mainText {
    height: 15px;
    color: black;
    font-size: 18px;
  }
  a {
    color: #36749d;
    font-size: 13px;
  }
  &.active {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
}

/* width */
::-webkit-scrollbar {
  width: 5px;
  overflow: auto;
}

/* Track */
::-webkit-scrollbar-track {
  background: #ffffff;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #36749C;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #ffffff;
}

.block {
  border-radius: 5px;
  border: 1px solid #b6cce3;
  background-color: white;
  float: left;
  height: 40vh;
  padding: 2% 0;
}

.detail {
  margin-bottom: 10px;
  border-radius: 0 10px 10px 0;
  background: #f0f5f9;
  padding: 15px;
  span {
    display: block;
  }

  min-height: 350px;
}

.active {
  color: #36749d;
}

.noActive {
  color: #b6cce3;
}
.subBlock {
  border-bottom: 1px solid #b6cce3;
  margin-left: 20px;
  margin-top: 20px;
  margin-right: 20px;
  .title {
    color: #36749d;
    font-weight: bold;
    font-size: 18px;
  }
}

.icon {
  width: 100px;
  height: 65px;
  margin: 10px auto 0 auto;

}

.plainBlock {
  font-weight: 500;
  padding: 10px 0;
  font-size: 14px;
  line-height: 15px;
  position: relative;
  > p {
    padding-right: 110px;
    margin-bottom: 5px;
    line-height: 16px;
  }
}

.button {
  position: absolute;
  right: 15px;
  top: 10px;
  border: 1px solid #36749d;
  color: #36749d;
  padding: 2px 15px;
  border-radius: 5px;
  font-size: 12px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  &.active {
    border: 1px solid #36749d;
    color: #36749d;
    background-color: #ffffff;
  }
}

.nascondi {
  background: white;
}

.button:hover {
  background: white;
}

.opzioni {
  background: url("/assets/img/optimaIcons/Modifica_hd.png") no-repeat center;
  background-size: contain;
}

.ricarica {
  background: url("/assets/img/optimaIcons/ricarica_hd.png") no-repeat center;
  background-size: contain;
}

button {
  padding-left: 0;
}

button a {
  margin-left: 15px;
}

/*.sim-data-title {
  color: #36749d;
}

.sim-data-value {
  color: #36749d;
}*/

.traffic-details-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #36749d;
}

.available-traffic-details-title {
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 5px;
  color: #36749d;
}

.block-flex {
  overflow-y: auto;
}

.display-flex {
  display: flex;
}

.tariff-details-control {
  .service-buttons-group {
    padding: 0;
    .service-button {
      border: 1px solid #36749d;
      width: 90px;
      height: 35px;
      background-color: white;
      border-radius: 5px;
      color: #36749d;
      margin-top: 15%;
      &.active {
        background-color: #36749d;
        color: #ffffff
      }
    }
  }
}

.chart-block {
  position: relative;
  width: 160px;
  height: 150px;
  float: inherit;
  .fa-caret-left, .fa-caret-right {
    font-size: 26px;
    line-height: 95px;
    cursor: pointer;
  }
  .fa-phone {
    margin-top: 100px;
    display: block;
    margin-right: 5px;
    font-size: 30px;
  }
  .fa-envelope {
    margin-top: 100px;
    display: block;
    margin-right: 5px;
    font-size: 27px;
  }
  .fa-arrow-reverse {
    margin-top: 43px;
    width: 40px;
    margin-left: -5px;
  }

}

.remaining-credit-block {
  margin-top: 3%;
  border: 1px solid #36749d;
  background-color: white;
  max-width: 90%;
  height: 50px;
  border-radius: 5px;
  div {
    display: inline-block;
    text-align: center;
    vertical-align: middle;
  }
  .remaining-credit-title {
    font-size: 20px;
    float: left;
    height: 100%;
    width: 68%;
    color: #36749d;
    margin-top: 7px;
  }
  .remaining-credit {
    font-size: 24px;
    color: #36749d;
    float: left;
    height: 100%;
    width: 32%;
    margin-top: 4.5px;
  }
}

.piano-tariffario {
  margin-top: 2%;
  border: 1px solid #36749d;
  background-color: white;
  max-width: 90%;
  height: 50px;
  border-radius: 5px;
  cursor: pointer;

  .piano-tariffario-text {
    font-size: 20px;
    float: left;
    height: 100%;
    width: 100%;
    color: #36749d;
    margin-top: 7px;
    text-align: center;
  }
}

.active-piano-tariffario {
  background-color: #36749d;
  .piano-tariffario-text {
    color: white;
  }
}

.piano-tariffario:hover {
  background-color: #36749d;
  .piano-tariffario-text {
    color: white;
  }
}

.summary-sim-block {
  margin-top: 1%;
  .offer {
    margin-bottom: 5px;
  }
  .mobile-summary-table {
    border: 1px solid #b6cce3;
    border-radius: 10px;
    color: #36749d;
    background: rgba(180, 203, 225, 0.2);
  }
  .border-info {
    font-size: 16px;
    color: #36749d;
    border-bottom: 1px solid rgb(180, 203, 225);
    padding-bottom: 10px;
    // margin-bottom: 10px;
    // background: rgba(180, 203, 225, 0.2);
    .info-title {
      font-weight: bold;
      margin-right: 2%;
    }

  }
  .table-title {
    font-size: 16px;
    font-weight: bold;
    color: #36749d;
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .table-column {
    padding-left: 25px;
    border-right: 1px solid rgb(180, 203, 225);
    margin-left: 5px;
    &:last-child {
      border-right: none;
    }
  }
  &:last-child {
    border-right: none;
  }
  .info {
    font-size: 12px;
  }
  .summary-table {
    margin-top: 2%;
    margin-bottom: 2%;
    // border: 1px solid rgb(180, 203, 225);
    // border-bottom: 1px solid #B0C7DD;;
  }
}

.tariff-plan {
  font-weight: bold;
}

.chart-label {
  position: absolute;
  font-size: 14px;
  color: #36749d;
  text-align: center;
  top: 22%;
  left: 17%;
  width: 115px;
  .live-amount {
    font-size: 19px;
    font-weight: bold;
  }
}

.navigation-block {
  > a {
    display: block;
  }
}

.unlimited-title {
  margin-top: 40%;
  font-size: 18px;
}

.sim-details {
    overflow-y: scroll;
    height: 430px;
    color: #36749d;
}

.form-control {
  border: 1px solid #B2C8DD;
  border-radius: 5px;
  font-weight: bold;
  color: #3A7597;
  font-size: 15px;
  height: 35px;
  width: 230px;
  margin-top: 5px;

  option {
    font-weight: bold;
  }
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

.add-5g-button {
  width: 123px;
  display: block;
  margin: 20px auto 0;
}

@media only screen and (max-width: 1050px) {
  .chart-block {
    margin: 0;
  }
  .navigation-block {
    > a {
      display: inline-block;
    }
  }
  .service-buttons-group {
    margin-right: 8%;
  }
  .remaining-credit-title {
    font-size: 18px;
  }
  .remaining-credit {
    font-size: 22px;
  }
}

@media only screen and (max-width: 500px) and (max-device-width: 500px) {
  .tariff-details-control .service-buttons-group .service-button {
    margin-top: 0;
    margin-left: 1%;
  }
  .block {
    height: 42vh;
  }
  .sim-details {
    height: 42vh;
  }
}

@media only screen and (max-width: 370px) {
  .titleBlock {
    visibility: hidden;
    position: absolute;
  }
}

@media only screen and (min-width: 321px) and (max-width: 369px) {
  .tariff-details-control {
    padding-left: 0;
    padding-right: 20px;
    .chart-block {
      margin-right: 0;
      margin-bottom: 20px;
      //left: 20%;
    }
  }
}

@media only screen and (max-width: 800px) and (max-device-width: 800px) {
  .sim-details {
    margin-bottom: 10px;
    &.active {
      border-bottom-right-radius: 5px;
      border-top-right-radius: 5px;
    }
  }
  .chart-block {
    margin-right: 21%;
  }
  .block {
    height: auto;
  }
  .remaining-credit-title {
    font-size: 20px;
  }
  .remaining-credit {
    font-size: 24px;
  }
}

@media only screen and (max-width: 400px) and (max-device-width: 400px) {
  .display-flex {
    display: block;
  }
}

/*@media only screen and (max-width: 400px) and (max-device-width: 400px) {
  .display-flex {
    display: block;
    .table-column {
      border-top: 1px solid rgb(180,203,225);
    }
    .table-column:first-child {
      border-top: none;
    }
  }
}*/
@media only screen and (max-width: 370px) {
  .summary-sim-block {
    .mobile-summary-table {
      background: none;
    }
    .summary-table {
      border: none;
      .table-column {
        margin-top: 1%;
        margin-left: 0;
        border: 1px solid rgba(180, 203, 225, 0.3);
        .offer {
          padding: 0;
        }
        .table-title {
          font-size: 13px;
          margin-bottom: 0;
        }
        .tariff-plan {
          font-size: 14px;
        }
        .offer {
          font-size: 14px
        }
      }
    }
    border: none;
    .border-info {
      font-size: 14px;
    }
  }
  .remaining-credit-block {
    height: 50px;
    margin-top: 20px;
    .remaining-credit-title {
      font-size: 12px;
      margin-top: 15px;
    }
    .remaining-credit {
      font-size: 15px;
      margin-top: 12px;
    }
  }
  .piano-tariffario {
    max-width: 100%;
  }
  .tariff-details-control {
    display: inline-grid;
    justify-content: center;
    .chart-block {
      margin: 0 0 12px;
    }
  }

}

@media only screen and (min-width: 370px) and (max-width: 476px) {
  .chart-block {
    position: relative;
    width: 87px;
    height: 91px;
    margin-right: 5px;

    .fa-arrow-reverse {
      margin-top: 57px;
      width: 25px;
      position: absolute;
      right: 73px;
    }
    .fa-envelope {
      margin-top: 60px;
      font-size: 19px;
      margin-left: -50px;
    }
    .fa-phone {
      font-size: 24px;
      margin-left: -45px;
      margin-top: 58px;
      display: block;
      float: left;
    }
    .fa-caret-right {
      position: absolute;
      font-size: 18px;
      margin-top: -16px;
      float: left;
      margin-left: -1px;
    }

    .fa-caret-left {
      position: absolute;
      font-size: 18px;
      margin-top: -16px;
      float: left;
      margin-left: -6px;
    }
  }

  .chart-title {
    font-size: 7px;
    margin-left: -10px;
    width: 50%;
    top: 5px;
  }

  .chart-label {
    font-size: 17px;
    font-weight: bold;
    .live-amount {
      font-size: 10px;
    }
  }

  .remaining-credit-block {
    height: 50px;
    max-width: 100%;
    margin-top: 10%;
    .remaining-credit-title {
      font-size: 15px;
      margin-top: 12px;
    }
    .remaining-credit {
      font-size: 17px;
      margin-top: 12px;
    }
  }

  .piano-tariffario {
    max-width: 100%;
  }

  .traffic-details-title {
    text-align: center;
  }
  .tariff-details-control {
    text-align: center;
    display: flex;
    justify-content: space-around;
  }
  .available-traffic-details-title {
    text-align: center;
  }
  .block-flex {
    overflow-y: visible;
  }

  .titleBlock {
    visibility: hidden;
    position: absolute;
  }
  .plainBlock {
    font-size: 12px;
    border-bottom: 1px solid rgba(180, 203, 225, 0.3);
  }
  .detail {
    border: none;
  }
  .summary-sim-block {
    .mobile-summary-table {
      background: none;
    }
    .summary-table {
      border: none;
      .table-column {
        margin-top: 1%;
        margin-left: 0;
        border: 1px solid rgba(180, 203, 225, 0.3);
        .offer {
          padding: 0;
        }
        .table-title {
          font-size: 13px;
          margin-bottom: 0;
        }
      }
    }
    border: none;

    .border-info {
      font-size: 14px;
     // display: flex;
    }
  }

  .display-flex {
    display: inline-table;
    font-size: 10px;
    .table-column {
      border: 1px solid rgba(180, 203, 225, 0.3);
    }
  }
  .table-title {
    font-size: 14px;
  }
  .tariff-plan {
    font-size: 14px;
  }
  .offer {
    font-size: 14px;
  }

  .selectedRecord {
    overflow: hidden;
  }
  .mobile-layout {
    float: left;
    width: 100%;
  }
  .plainBlock {
    padding-left: 15px;
    padding-right: 15px;
  }
  .form-control {
    margin: 0 auto;
  }
}

@media only screen and (min-width: 477px) and (max-width: 767px) {
  .chart-block {
    width: 123px;
    height: 139px;
    margin-right: 5px;
    .chart-label {
      position: absolute;
      font-size: 14px;
      color: #36749d;
      text-align: center;
      top: 22%;
      left: 7%;
      width: 115px;
      .live-amount {
        font-size: 15px;
      }
    }
    .fa-arrow-reverse {
      margin-top: 82px;
      width: 40px;
      left: 39px;
      position: absolute;
    }
    .fa-envelope {
      margin-top: 91px;
      display: block;
      margin-right: 5px;
      font-size: 27px;
    }
    .fa-phone {
      margin-top: 91px;
      display: block;
      right: 40px;
      font-size: 32px;
      position: absolute;
    }
  }
  .tariff-details-control {
    text-align: center;
    display: flex;
    justify-content: space-around;
  }
  .remaining-credit-block {
    margin-top: 10%;
  }
  .block-flex {
    overflow-y: visible;
  }

  .piano-tariffario {
    max-width: 100%;
  }

  .titleBlock {
    visibility: hidden;
    position: absolute;
  }
  .plainBlock {
    font-size: 12px;
    border-bottom: 1px solid rgba(180, 203, 225, 0.3);
  }
  .detail {
    border: none;
  }
  .summary-sim-block {
    .mobile-summary-table {
      background: none;
    }
    .summary-table {
      border: none;
      .table-column {
        margin-top: 1%;
        margin-left: 0;
        .offer {
          padding: 0;
        }
      }
    }
    border: none;

    .border-info {
      font-size: 14px;
     // display: flex;
    }
  }
  .display-flex {
    display: inline-table;
    font-size: 10px;
    .table-column {
      // border: 1px solid rgba(180, 203, 225, 0.3);
      border: none;
    }
  }
  .table-title {
    font-size: 14px;
  }
  .tariff-plan {
    font-size: 14px;
  }
  .offer {
    font-size: 14px;
  }
  .selectedRecord {
    overflow: hidden;
  }
  .mobile-layout {
    float: left;
    width: 100%;
  }
  .plainBlock {
    padding-left: 15px;
    padding-right: 15px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tariff-plan {
    font-size: 14px;
  }
  .offer {
    font-size: 14px
  }
  .chart-block {
    position: relative;
    width: 193px;
    height: 180px;
    margin-right: 5px;

    .fa-arrow-reverse {
      margin-top: 114px;
      width: 41px;
      position: absolute;
      left: 51px;
    }
    .fa-envelope {
      margin-top: 125px;
      font-size: 29px;
      margin-left: 22px;
    }
    .fa-phone {
      font-size: 36px;
      margin-left: 54px;
      margin-top: 121px;
      position: absolute;
    }
  }

  .block-flex {
    height: 475px;
  }

  .chart-title {
    font-size: 14px;
    margin-left: 10px;
  }
  .chart-label {
    font-size: 17px;
    font-weight: bold;
    .live-amount {
      font-size: 23px;
    }
  }
  .remaining-credit-block {
    height: 50px;
    max-width: 100%;
    margin-top: 20px;
  }

  .piano-tariffario {
    max-width: 100%;
  }

  .traffic-details-title {
    text-align: center;
  }
  .tariff-details-control {
    text-align: center;
    display: flex;
    justify-content: space-around;
  }
  .available-traffic-details-title {
    text-align: center;
  }
  .fa-caret-right {
    position: absolute;
    bottom: -30px;
    left: 122px;
  }

  .fa-caret-left {
    position: absolute;
    top: 13px;
    right: 3px;
  }
  .selectedRecord {
    display: contents;
  }
  .remaining-credit-block {
    margin-top: 10%;
  }
  .block-flex {
    overflow-y: visible;
  }

  .titleBlock {
    visibility: hidden;
    position: absolute;
  }

  .plainBlock {
    font-size: 12px;
    border-bottom: 1px solid rgba(180, 203, 225, 0.3);
  }
  .detail {
    border: none;
  }
  .summary-sim-block {
    .mobile-summary-table {
      background: none;
      border: none;
      margin-top: 2%;
    }
    .summary-table {
      border: none;
      background: none;
      padding: 0;
      .table-column {
        margin-top: 1%;
        margin-left: 0;
        border: none;
        .offer {
          padding: 0;
        }
      }
    }
    border: none;

    .border-info {
      font-size: 14px;
      border-bottom: 0;
      //display: flex;
    }
  }

  .display-flex {
    display: block;
    font-size: 10px;
    .table-column {
      border: 1px solid rgba(180, 203, 225, 0.3);
    }
  }
  .table-title {
    font-size: 14px;
  }

  .selectedRecord {
    overflow: hidden;
  }
  .mobile-layout {
    float: left;
    width: 100%;
  }
  .plainBlock {
    padding-left: 15px;
    padding-right: 15px;
  }
  .sim-details {
    width: 100%;
  }
  .row:before {
    content: none;
  }
  .row:after {
    content: none;
  }
  .form-control {
    margin: 0 auto;
  }
  .navigation-block {
    margin-top: 10px
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-details {
    width: 50%;
  }
  .hidden-xs {
    width: 50%;
  }
  .col-md-12 {
    width: 100%;
  }
  .chart-block {
    position: relative;
    width: 83px;
    height: 90px;
    float: inherit;
    .fa-arrow-reverse {
      margin-top: 60px;
      width: 26px;
      position: absolute;
      left: 27px;
    }
    .fa-envelope {
      margin-top: 64px;
      display: block;
      margin-right: 2px;
      font-size: 19px;
    }
    .fa-phone {
      font-size: 23px;
      position: absolute;
      margin-top: 62px;
      margin-left: 28px;
    }
    .fa-caret-right {
      font-size: 19px;
      line-height: 76px;
      cursor: pointer;
      position: absolute;
    }
    .fa-caret-left {
      font-size: 19px;
      line-height: 77px;
      cursor: pointer;
      position: absolute;
    }
  }
  .chart-label {
    position: absolute;
    font-size: 9px;
    color: #36749d;
    text-align: center;
    top: 17%;
    left: 8%;
    width: 74px;
    .live-amount {
      font-size: 11px;
    }
  }
  .chart-title {
    top: 7pt;
  }

  .tariff-details-control {
    display: flex;
    justify-content: space-around;
  }

  .remaining-credit-block {
    // margin-top: 7%;
    border: 1px solid #36749d;
    background-color: white;
    max-width: 100%;
    height: 50px;
    border-radius: 5px;
    .remaining-credit {
      font-size: 17px;
      color: #36749d;
      float: left;
      height: 100%;
      width: 32%;
      margin-top: 7.5px;
    }
    .remaining-credit-title {
      font-size: 17px;
      float: left;
      height: 100%;
      width: 68%;
      color: #36749d;
      margin-top: 7px;
    }

  }

  .piano-tariffario {
    max-width: 100%;
  }

  .available-traffic-details-title {
    font-size: 17px;
  }
  .sim-details {
    width: 100%;
  }

}

@media only screen and (min-width: 1200px) and (max-width: 1730px) {
  .chart-block {
    position: relative;
    width: 104px;
    height: 112px;
    float: inherit;

    .chart-title {
      margin-left: -11px;
      font-size: 11px;
      width: 62%;
      top: 4px;
      .live-amount {
        font-size: 15px;
        font-weight: bold;
      }
    }
  ;
    .fa-arrow-reverse {
      top: 67px;
      width: 31px;
      position: absolute;
      right: 64px;
      margin: auto;
    }
    .fa-envelope {
      top: 75px;
      right: 69px;
      font-size: 21px;
      position: absolute;
      margin: auto;
    }
    .fa-phone {
      margin: auto;
      top: 71px;
      right: 66px;
      font-size: 28px;
      position: absolute
    }
    .fa-caret-right {
      font-size: 23px;
      line-height: 78px;
      cursor: pointer;
    }
    .fa-caret-left {
      font-size: 23px;
      line-height: 78px;
      cursor: pointer;
      margin-left: -18px;
    }
  }

  .tariff-details-control {
    display: flex;
    justify-content: space-around;
  }

  .remaining-credit-block {
    // margin-top: 7%;
    border: 1px solid #36749d;
    background-color: white;
    max-width: 100%;
    height: 50px;
    border-radius: 5px;
  }

  .block-flex {
    overflow-y: visible;
  }

  .piano-tariffario {
    max-width: 100%;
  }

}

@media only screen and (max-width: 320px) and (max-device-width: 320px) {
  .chart-block {
    position: relative;
    width: 87px;
    height: 91px;
    margin-right: 0;
  }
}

@media only screen and (max-width: 768px) {
  .remaining-credit-block {
    height: 50px;
    max-width: 100%;
  }
  .piano-tariffario{
    max-width: 100%;
  }
  .service-details {
    padding: 0;
  }
  .summary-sim-block {
    .mobile-summary-table {
      border: none;
      padding: 0;
    }
  }
  .record-row {
    &.active {
      background-color: #f0f5f9;
    }
  }
  .detail {
    border-radius: 5px;
  }
  .sim-details {
    border: none;
    overflow-y: auto;
    height: 100%;
  }
  .available-traffic-details-title {
    margin: 0;
  }
  .traffic-details-title {
    line-height: 25px;
  }
  .service-button {
    margin-bottom: 10px;
  }
}

@media only screen and (max-width: 400px) {
  .summary-sim-block {
    .table-column {
      border: none;
    }
  }
}

@media only screen and (min-width: 768px) {
  .selectedRecord.sim-details {
    border-right: 0;
    border-radius: 10px 0 0 10px;
  }
  .service-details {
    padding-right: 0;
    padding-left: 0;
  }
}
