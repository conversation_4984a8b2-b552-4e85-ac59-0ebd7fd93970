import {Component, OnDestroy} from '@angular/core';
import {select} from '@angular-redux/store';
import {Subscription} from 'rxjs/Subscription';
import {Observable} from 'rxjs/Observable';
import * as _ from 'lodash';
import {UserData} from '../../../../common/model/userData.model';
import {EnergiaService} from '../../../../common/services/energia/energia-service.service';
import {Utility} from '../../../../common/model/services/userServices.model';
import {ServiceStatus} from '../../../../common/enum/ServiceStatus';
import {EnergiaType} from '../../../../common/enum/EnergiaType';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {ServicesActions} from '../../../../redux/services/actions';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {
  changeTitleInChartByHours,
  chartConfiguration,
  chartConfigurationByDays,
  chartConfigurationByHours,
  energyLabelsAndDatasetEnricher,
  energyLablesDatasetEnricherByDays,
  energyLablesDatasetEnricherByHours,
  normalizeDetailsByHours,
  normalizeEnergyAdjustments,
  normalizeEnergyDetailsByDays,
  normalizeEnergyDetailsByDaysForCurrentMonth,
  normalizeKeysEnergyDetailsByDays,
} from '../../charts/chartsConfiguration';
import {ChartBuilder} from '../../../../common/utils/chart-builder/chart-builder';
import {Offer} from '../../../../redux/model/Offer';
import {HomeService} from '../home/<USER>';
import {PaginatorBuilder} from '../../../../common/builders/PaginatorBuilder';
import {Paginator} from '../../../../common/utils/pagination/Paginator';
import ChartConfig from '../../../../common/utils/chart-builder/ChartConfig';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {EtichettaLabelService} from '../../../../common/services/etichetta-label/etichetta-label.service';
import {EtichettaLabel} from '../../../../common/model/etichetta-label/etichetta-label.model';
import {UserDataService} from '../../../../common/services/user-data/userData.service';
import {
  EnergyDetails,
  EnergyPointAdjustment,
  EnergyPointAdjustmentObject
} from '../../../../common/model/energy/EnergyPointAdjustment';
import * as moment from 'moment';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {forkJoin} from 'rxjs/observable/forkJoin';


@Component({
  selector: 'app-luce-layout',
  templateUrl: './luce-layout.component.html',
  styleUrls: ['./luce-layout.component.scss']
})
export class LuceLayoutComponent implements OnDestroy {

  @select(['services'])
  services: Observable<ServiceStateModel>;

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  @select(['user', 'activeOffers'])
  private activeOffers;

  chartConfig: ChartConfig;
  chartConfigByDays: ChartConfig;
  chartConfigByHours: ChartConfig;

  currentMonth: number;
  currentYear: number;
  currentDay: number;

  hasAdjustments: number;
  hasDetailsByDay: boolean;
  ModalWindow: boolean;
  showLeftArrow: boolean;
  showRightArrow: boolean;

  checkid: number;
  podDetails;
  sortedServices = [];
  userCluster: string;
  selectedDate: string;
  dateRestrictions: string;
  pdf: Array<any>;
  detail: Utility;
  detailsByDay: boolean;
  detailsByHours: boolean;
  showConsumiDelInCorso: boolean;
  detailByMonth = true;
  showAdditionalInfo: boolean;
  emptyEnergyOffers: boolean;
  offers: Array<Offer>;
  formExelData: FormGroup;

  chartPaginator: Paginator;

  consumptionAnnotation: number;

  showEtichetta: boolean;
  limit: number;

  serviceSubscription: Subscription;
  userInfoSubscription: Subscription;
  clientOffersSubscription: Subscription;
  clickEventSubscriptionMonthsChart: Subscription;
  clickEventSubscriptionDaysChart: Subscription;


  etichettaLabelByUtNumberMap = new Map<string, EtichettaLabel>();
  etichettaFormGroups: Array<FormGroup> = [];

  constructor(private serviceActions: ServicesActions, private luceService: EnergiaService,
              private homeServices: HomeService, private formBuilder: FormBuilder,
              private etichettaLabelService: EtichettaLabelService, private userDataService: UserDataService) {
    this.userInfoSubscription = this.userInfo.subscribe(userInfo => {
      if (userInfo && userInfo.cluster) {
        this.userCluster = userInfo.cluster.value;
      }
    });
    this.clientOffersSubscription = this.activeOffers.subscribe(activeOffers => {
      this.offers = activeOffers;
    });
    this.serviceSubscription = this.services.subscribe(userServices => {
      const {
        services,
        lucePodDetailsLoaded,
        lucePodDetailsLoading,
        lucePodDetails,
        activeServices,
        servicesLoaded
      } = userServices;
      this.podDetails = lucePodDetails;
      this.sortedServices = services
        .filter(service => (service.serviceName === EnergiaType.ENERGIA || service.serviceName === EnergiaType.ELETTRICITA))
        .map(service => {
          service.utilities = service.utilities
            .filter(utility => utility.status === ServiceStatus.ATTIVATO || utility.status === ServiceStatus.IN_ATTIVAZIONE);
          return service;
        });
      if (this.sortedServices) {
        this.sortedServices.forEach(service => {
          service.utilities.forEach(utility => {
            let formGroup = this.formBuilder.group({
              etichetta: [null, [Validators.minLength(3), Validators.maxLength(16)]]
            });
            this.etichettaFormGroups.push(formGroup);
            this.etichettaLabelService.getEtichettaLabelByPodUtNumber(utility.utNumber).subscribe(etichetta => {
              if (etichetta) {
                formGroup.controls['etichetta'].setValue(etichetta.description);
                this.etichettaLabelByUtNumberMap.set(utility.utNumber, etichetta);
              }
            });
          });
        });
      }
      if (servicesLoaded && !lucePodDetailsLoaded && !lucePodDetailsLoading) {
        this.serviceActions.loadLucePodDetails(activeServices[EnergiaType.ENERGIA]);
      }
    });
    this.userDataService.getUserCondominioInfo(localStorage.getItem('clientId')).subscribe(condominioList => {
      if (condominioList !== null && condominioList.length !== 0) {
        for (const item of condominioList) {
          if (item.customerId.toString() === localStorage.getItem('clientId') && item.sottoTipoCluster === 'Condominio') {
            this.showEtichetta = true;
          }
        }
      }
    });
    this.clickEventSubscriptionMonthsChart = EnergiaService.getClickEventFromMonthsChart().subscribe(response => {
      this.getDetailsToBuiltChartByDays(response);
    });
    this.clickEventSubscriptionDaysChart = EnergiaService.getClickEventFromDaysChart().subscribe(response => {
        this.getDetailsToBuiltChartByHours(response);
      }
    );
    this.formExelData = new FormGroup({
      Da: new FormControl('', [Validators.required]),
      A: new FormControl('', [Validators.required])
    });
    const restrictionDay = (parseInt(moment().format('DD'), 10) - 1);
    this.dateRestrictions = moment().format('YYYY-MM-') + (restrictionDay < 10 ? '0' + restrictionDay : restrictionDay);
  }

  selectValue(utility: Utility, i: number) {
    this.hideDetailsByDay();
    this.checkid = i;
    this.pdf = this.setPDFList('ENERGIA');
    this.detail = utility;
    if (this.podDetails[utility.utNumber]) {
      this.luceService.loadLucePodDetails([utility]).subscribe(podDetails => {
        const filteredPodDetails = podDetails.filter(podDetail => podDetail.descrizione === utility.utNumber);
        if (filteredPodDetails.length > 0) {
          const puntoValues = filteredPodDetails.map(detail => detail.punto);
          const mergedResponse = [];
          const adjustmentObservables = puntoValues.map(punto =>
            this.luceService.energyPointAdjustments(localStorage.getItem('clientId'), punto)
          );
          forkJoin(adjustmentObservables).subscribe(adjustmentResponses => {
            adjustmentResponses.forEach(response => mergedResponse.push(...response));
            const ConsumiDelInCorsoMonth = moment(moment(), 'MMM-YYYY').toDate();
            const startDay = '1/' + moment().format('MM/YYYY');
            const endDay = ((parseInt(moment().format('DD'), 10) - 1) === 0 ? '1' : parseInt(moment().format('DD'), 10) - 1) + '/' + moment().format('MM/YYYY');
            this.luceService.energyDetailsByHours(localStorage.getItem('clientId'), this.detail.utNumber, startDay, endDay).subscribe(monthResponse => {
              const result = normalizeEnergyDetailsByDaysForCurrentMonth(monthResponse);
              for (const item of this.offers) {
                if (item.ee === null) {
                  this.emptyEnergyOffers = true;
                } else {
                  this.emptyEnergyOffers = false;
                  break;
                }
              }

              if (result !== 0 && (this.offers.length === 0 || this.emptyEnergyOffers)) {
                this.showConsumiDelInCorso = false;
                const additionalData = EnergyPointAdjustmentObject(parseInt(moment().format('YYYY'), 10), ConsumiDelInCorsoMonth, parseInt(result.toFixed(0), 10));
                mergedResponse.push(additionalData);
              } else {
                this.showConsumiDelInCorso = true;
              }
              this.buildChart(utility.utNumber, mergedResponse);
            });
          });
        }
      });
    }
  }

  getDetailsToBuiltChartByDays(response: number) {
    const startDay = '1/' + (moment(response, 'MMMYYYY').month() + 1) + '/' + moment(response, 'MMMYYYY').year();
    const endDay = moment(response, 'MMMYYYY').daysInMonth() + '/' + (moment(response, 'MMMYYYY').month() + 1) + '/' + moment(response, 'MMMYYYY').year();
    this.currentMonth = moment(response, 'MMMYYYY').month() + 1;
    this.selectedDate = moment().month(this.currentMonth - 1).locale('it').format('MMMM').toUpperCase();
    this.currentYear = moment(response, 'MMMYYYY').year();
    this.luceService.energyDetailsByHours(localStorage.getItem('clientId'), this.detail.utNumber, startDay, endDay).subscribe(
      responseBody => this.buildChartByDays(responseBody));
  }

  getDetailsToBuiltChartByHours(day: number) {
    this.currentDay = day;
    const startEndDay = day + '/' + this.currentMonth + '/' + this.currentYear;
    this.luceService.energyDetailsByHours(localStorage.getItem('clientId'), this.detail.utNumber, startEndDay, startEndDay).subscribe(
      response => {
        if (response.length !== 0) {
          this.buildChartByHours(response);
          this.activateDetailsByHours();
        }
      });
  }

  buildChartByDays(detailsByDays: Array<EnergyDetails>) {
    if (detailsByDays.length === 0) {
      this.hasDetailsByDay = true;
      this.hideDetailsByDay();
    } else {
      this.limit = detailsByDays.length;
      this.hasDetailsByDay = false;
      const keys = normalizeKeysEnergyDetailsByDays(detailsByDays);
      const result = normalizeEnergyDetailsByDays(detailsByDays);
      const builder = ChartBuilder.builder().withConfiguration(chartConfigurationByDays())
        .withEnricher(energyLablesDatasetEnricherByDays(_.pick(result, keys)));
      this.chartConfigByDays = builder.build();
      this.activateDetailsByDay();
      this.hideDetailsByHours();
      this.showAdditionalInfo = detailsByDays[0].ka !== 1 || detailsByDays[0].kr !== 1 || detailsByDays[0].kr !== 1;
    }
  }

  buildChartByHours(details: Array<EnergyDetails>) {
    const normalizedDetailsByHours = normalizeDetailsByHours(details);
    if (normalizedDetailsByHours) {
      const normalizedDetailsByHoursKeys = Object.keys(normalizedDetailsByHours);
      const builder = ChartBuilder.builder().withConfiguration(chartConfigurationByHours())
        .withEnricher(energyLablesDatasetEnricherByHours(_.pick(normalizedDetailsByHours, normalizedDetailsByHoursKeys)));
      this.chartConfigByHours = builder.build();
      this.activateDetailsByHours();
      this.showRightArrow = this.currentDay < this.limit;
      this.showLeftArrow = this.currentDay > 1;
    }
  }

  buildChart(pod: string, adjustments: Array<EnergyPointAdjustment>) {
    const normalizedAdjustments = normalizeEnergyAdjustments(adjustments);
    if (normalizedAdjustments) {
      const normalizedAdjustmentsKeys = Object.keys(normalizedAdjustments);
      this.hasAdjustments = normalizedAdjustmentsKeys && normalizedAdjustmentsKeys.length;
      this.chartPaginator = PaginatorBuilder.builder().yearlyPagination().reversive().forArray(normalizedAdjustmentsKeys).withStep(12)
        .withOnChange(array => {
          const builder = ChartBuilder.builder().withConfiguration(chartConfiguration())
            .withEnricher(energyLabelsAndDatasetEnricher(_.pick(normalizedAdjustments, array)));
          const found = this.offers.filter(item => item.ee && item.ee.every(ee => ee.utenza === pod));
          if (found && found.length) {
            this.consumptionAnnotation = found[0].ee[0].kwh;
          }
          this.chartConfig = builder.build();
        }).build();
    }
  }

  setPDFList(serviceName) {
    return this.homeServices.getPDFList(serviceName, this.userCluster);
  }

  hide() {
    this.detail = null;
  }

  ngOnDestroy() {
    ObservableUtils.unsubscribeAll([this.serviceSubscription, this.userInfoSubscription,
      this.clientOffersSubscription, this.clickEventSubscriptionMonthsChart, this.clickEventSubscriptionDaysChart]);
  }

  changeEtichetta() {
    return undefined;
  }

  getEtichettaLabelByUtNumber(utNumber: string): EtichettaLabel {
    return this.etichettaLabelByUtNumberMap.get(utNumber);
  }

  checkForEditingFields(event: any, utNumber: string, description: string, etichettaLabel: EtichettaLabel) {
    if (event.action == 'accept') {
      if (!etichettaLabel) {
        etichettaLabel = new EtichettaLabel(utNumber);
      }
      etichettaLabel.description = description;
      this.etichettaLabelService.createOrUpdateEtichettaLabel(etichettaLabel).subscribe();
    }
  }

  activateDetailsByDay() {
    this.detailsByDay = true;
    this.detailByMonth = false;
  }

  activateDetailsByHours() {
    this.detailsByHours = true;
  }

  hideDetailsByHours() {
    this.detailsByHours = false;
  }

  downloadFile() {
    if (this.formExelData.valid) {
      this.luceService.getExcelFile(localStorage.getItem('clientId'), this.detail.utNumber, moment(this.formExelData.value.Da).format('DD/MM/YYYY'), moment(this.formExelData.value.A).format('DD/MM/YYYY')).subscribe(res => {
        const blob = new Blob([res], {type: 'application/vnd.ms-excel'});
        const data = window.URL.createObjectURL(blob);
        const name = document.createElement('a');
        name.download = 'Report.xls';
        name.href = data;
        name.click();
      });
      this.hideDialogModal();
    } else {
      FormUtils.setFormControlsAsTouched(this.formExelData);
    }
  }

  previousPage() {
    changeTitleInChartByHours(+this.currentDay - 1);
    this.getDetailsToBuiltChartByHours(this.currentDay - 1);
  }

  nextPage() {
    changeTitleInChartByHours(+this.currentDay + 1);
    this.getDetailsToBuiltChartByHours(+this.currentDay + 1);
  }

  hideDetailsByDay() {
    this.detailsByDay = false;
    this.detailsByHours = false;
    this.detailByMonth = true;
  }

  showModalWindow() {
    this.ModalWindow = true;
  }

  hideDialogModal() {
    this.ModalWindow = false;
  }
}
