import {Component, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {Subscription} from 'rxjs/Subscription';
import 'rxjs/add/observable/empty';
import * as R from 'ramda';
import {UserData} from '../../../../common/model/userData.model';
import {UserDataService} from '../../../../common/services/user-data/userData.service';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {IncidentEventCategory} from '../../../../common/enum/IncidentEventCategory';
import {ChangePasswordIdentificationRequest, ChangePersonalDataRequest, Password} from '../../../../common/model/ChangePersonalDataRequest';
import {UserActions} from '../../../../redux/user/actions';
import {NotificationService} from '../../../../common/services/notification/notification.service';
import {AddressType} from '../../../../common/enum/AdderssType';
import {Cluster} from '../../../../common/enum/Cluster';
import {BillingType} from '../../../../common/enum/BillingType';
import {ServiceResponseStatus} from '../../../../common/enum/ServiceResponseStatus';
import Validator from '../../../../common/utils/Validator';
import {Address} from '../../model/Address';
import {IncidentEventResponse} from '../../../autolettura/model/IncidentEventResponse';
import {ChangePersonalDataResponse} from '../../../../common/model/ChangePersonalDataResponse';
import {ModalEntity} from '../../../../common/model/modal/ModalEntity';
import {ModalActions} from '../../../../redux/modal/actions';
import {ActivatedRoute} from '@angular/router';
import {debitRequestSucceedModalText} from '../../config/config';


const rewriteUserData = R.compose(
  R.ifElse(
    R.propEq('fiscalCode', ''),
    R.omit(['fiscalCode']),
    R.omit(['vatNumber'])
  ));

const modalitaFatturaErrorMessage = `Non è possibile attivare la spedizione elettronica della fattura Optima per
 mancanza della mail tra i tuoi dati. Per procedere registra la mail cliccando su “Modifica E-mail"`;

const modalitaFatturaValidator = (input: FormControl, parameters: UserData) => {
  return input.value === BillingType.ELECTRONICA && !parameters.email;
};
const errorMessage = 'Errore nel salvataggio della variazione.';

const successMessage = 'La tua richiesta di variazione è stata registrata. Visualizzerai il nuovo dato entro qualche minuto. Grazie.';

const emailPattern = '^\\w+[\\w-\\.]*\\@\\w+((-\\w+)|(\\w*))\\.[a-z]{2,3}$';

const pecPattern = '^[0-9a-zA-ZàèéìíîòóùúÀÈÉÌÍÎÒÓÙÚ_-]{3,}@[0-9a-zA-ZàèéìíîòóùúÀÈÉÌÍÎÒÓÙÚ_-]{3,15}.[0-9a-zA-ZàèéìíîòóùúÀÈÉÌÍÎÒÓÙÚ]{2,5}';

@Component({
  selector: 'app-user-data',
  templateUrl: './userData.component.html',
  styleUrls: ['./userData.component.scss']
})
export class UserDataComponent implements OnDestroy, OnInit {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  @select(['user', 'clientOffers'])
  private clientOffers;

  @select(['services', 'services'])
  activeServices: Observable<Array<any>>;

  userData: UserData;

  showAddress: boolean;

  incidentEventCategory = IncidentEventCategory;

  modalitaFatturaOptions = [BillingType.PRINT_LIST, BillingType.ELECTRONICA];

  officeAddress: string;

  indirizzoFatturazioneOptions;

  formGroup: FormGroup;

  isBusiness: boolean;

  private active = [];

  @ViewChild('infoModal') infoModal;

  userInfoSubscription: Subscription;

  clientOffersSubscription: Subscription;

  creditCardBrand: string;

  hasCreditCardInfo;

  isCreditCardEnabled: Observable<Boolean>;

  shouldShowModalitaPagamentoRow: boolean = true;

  readyToEdit: boolean = false;

  constructor(private service: UserDataService,
              private fb: FormBuilder, private userActions: UserActions,
              private notificationService: NotificationService,
              private modalActions: ModalActions,
              private route: ActivatedRoute) {

    const creditCardBrandMap = new Map();
    creditCardBrandMap.set('VISA', 'visa-icon');
    creditCardBrandMap.set('MASTERCARD', 'mastercard-icon');
    creditCardBrandMap.set('AMEX', 'amex-icon');

    this.userInfoSubscription = this.userInfo.subscribe(userData => {
      if (userData) {
        this.formGroup = this.fb.group({
          cliente: [userData.nameInInvoice, {
            validators: [Validators.required, Validator.notEmpty(), Validators.maxLength(100),
              Validators.minLength(3), Validator.onlyTextNumberAllowed()]
          }],
          modalitaFattura: [userData.billingType, {validators: [Validator.invalidIf(modalitaFatturaValidator, userData, modalitaFatturaErrorMessage)]}],
          indirizzoFatturazione: [null, {
            validators: [Validators.required, Validator.notEmpty(), Validators.maxLength(100),
              Validators.minLength(10)]
          }],
          indirizzoFatturazioneNew: [],
          officeAddress: [null, {
            validators: [Validators.required, Validator.notEmpty(), Validators.maxLength(100),
              Validators.minLength(10)]
          }],
          officeAddressNew: [],
          numeriRiferimento: [userData.phoneNumber ? userData.phoneNumber : userData.mobileNumber, {
            validators: [Validators.required, Validator.notEmpty(),
              Validator.digits(),
              Validators.minLength(8), Validators.maxLength(15)]
          }],
          email: [userData.email, {
            validators: [Validators.required, Validator.notEmpty(),
              Validators.pattern(emailPattern),
              Validators.minLength(10), Validators.maxLength(100),
            ]
          }],
          passwordIdentification: [userData.passwordIdentification, {
            validators: [Validators.required, Validator.notEmpty()
            ]
          }],
          pec: [userData.pec, {
            validators: [Validators.required, Validator.notEmpty(),
              Validators.minLength(10), Validators.maxLength(100),
              Validators.pattern(pecPattern)]
          }],
          recipientCode: [userData.recipientCode,
            [Validators.required, Validator.notEmpty(),
              Validators.minLength(7), Validators.maxLength(7)]]
        });
        this.userData = rewriteUserData(userData);
        this.isBusiness = userData.cluster && (userData.cluster.value === Cluster.BUSINESS || userData.cluster.value === Cluster.PUBLIC_ADMINISTRATION);
        this.showAddress = userData.billingType === BillingType.PRINT_LIST && userData.addresses && userData.addresses.length > 0;
        if (this.showAddress) {
          const addresses = userData.addresses.filter(address => address.tipo === AddressType.BILING);
          if (addresses.length > 0) {
            this.formGroup.controls.indirizzoFatturazione.setValue(addresses[0].indirizzo);
            this.indirizzoFatturazioneOptions = addresses.map(i => i.indirizzo);
          }
        }
        const officeAddressesOptions = userData.addresses.filter(address => address.tipo === AddressType.REGITERED_OFFICE)
          .map(i => i.indirizzo);
        if (officeAddressesOptions) {
          this.formGroup.controls.officeAddress.setValue(officeAddressesOptions[0]);
          this.officeAddress = officeAddressesOptions[0];
        }
        if (userData.paymentData) {
          this.creditCardBrand = creditCardBrandMap.get(userData.paymentData.brand);
          this.hasCreditCardInfo = userData.paymentData.expireYear && userData.paymentData.expireMonth &&
            userData.paymentData.maskedPan;
        }
      }
    });

    this.clientOffersSubscription = this.clientOffers.subscribe((response: any) => {
      if (response.length > 0) {
        const mills = new Date().getTime();
        this.active = response.filter(value => value.scadenzaAnnoContrattuale > mills || value.scadenzaAnnoContrattuale == null);
      }
    });

    this.activeServices.subscribe(services => {
      if (services && services.length === 1 && services[0].serviceName === 'MOBILE') {
        this.shouldShowModalitaPagamentoRow = false;
      }
    });

  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      if (params['success'] && params['success'] === 'true') {
        this.modalActions.showModal({
          text: debitRequestSucceedModalText
        } as ModalEntity);
      }
    });

    this.route.params.subscribe(param => {
      let actionParam = param['actionParam'];
      if (actionParam === 'editField') {
        this.readyToEdit = true;
      }
      if (actionParam === 'opemModalPagamento') {
        if(this.infoModal) {
          this.infoModal.showHideDialog();
        }
      }
    })
  }

  switchDisplay() {
    this.isCreditCardEnabled = this.service.getCreditCardStatus();
    this.infoModal.showHideDialog();
  }

  getModalitaPagamentoPdf() {
    if (this.userData.cluster.value === Cluster.BUSINESS) {
      return 'http://www.optimaitalia.com/public/files/pdf/moduli/Business/Modulo%20Variazione%20modalit%C3%A0%20di%20pagamento.pdf';
    }
    if (this.active) {
      return 'http://www.optimaitalia.com/public/files/pdf/moduli/Consumer/Modulo%20Variazione%20modalit%C3%A0%20di%20pagamento.pdf';
    }
    return 'http://www.optimaitalia.com/public/files/pdf/moduli/Consumer/Modulo%20Variazione%20modalit%C3%A0%20di%20pagamento.pdf';
  }

  getDocument() {
    window.open(this.getModalitaPagamentoPdf());
  }

  changePersonalData(incidentEventCategory: IncidentEventCategory) {
    return (value, oldValue) => {
      const request = {
        clientId: localStorage.getItem('clientId'), incidentEventCategory, value, oldValue
      } as ChangePersonalDataRequest;
      return this.buildOnAccept(request);
    };
  }

  changeShipmentMethod(incidentEventCategory: IncidentEventCategory) {
    return (value, oldValue) => {
      const request = {
        clientId: localStorage.getItem('clientId'),
        incidentEventCategory,
        value: value.toUpperCase(),
        oldValue
      } as ChangePersonalDataRequest;
      return this.buildOnAccept(request);
    };
  }

  changePasswordIdentification() {
    return () => {
      const request = {
        customerId: localStorage.getItem('clientId'),
        change: new Password(this.formGroup.get('passwordIdentification').value)
      } as ChangePasswordIdentificationRequest;
      return Observable.of({}).flatMap(() => {
        return this.service.changePasswordIdentification(request).catch(() => {
          this.notificationService.showInfoMessage(errorMessage);
          return Observable.throw({} as ChangePersonalDataResponse);
        });
      }).flatMap(response => {
        if (response.errorStatus && response.errorStatus.code === '200') {
          this.notificationService.showSuccessMessage(successMessage);
          return this.service.getUserData();
        }
        this.notificationService.showInfoMessage(errorMessage);
        return Observable.empty();
      }).flatMap(response => {
        this.userActions.userInfoLoaded(response as UserData);
        return Observable.empty();
      });
    };
  }


  buildOnAccept(request) {
    return Observable.of({}).flatMap(() => {
      return this.service.changeUserPersonalData(request).catch(() => {
        this.notificationService.showInfoMessage(errorMessage);
        return Observable.throw({} as ChangePersonalDataResponse);
      });
    }).flatMap(response => {
      if (response.errorStatus && response.errorStatus.code === '200') {
        this.notificationService.showSuccessMessage(successMessage);
        return this.service.getUserData();
      }
      this.notificationService.showInfoMessage(errorMessage);
      return Observable.empty();
    }).flatMap(response => {
      this.userActions.userInfoLoaded(response as UserData);
      return Observable.empty();
    });
  }

  changeAddress(incidentEventCategory: IncidentEventCategory) {
    return (value: Address, oldValue?: string) => {
      const request = new ChangePersonalDataRequest();
      request.clientId = localStorage.getItem('clientId');
      request.addressToChange = incidentEventCategory === IncidentEventCategory.CHANGE_OFFICE_ADDRESS ? this.officeAddress : oldValue;
      request.newAddress = `${value.toponimo},${value.via},${value.civico},${value.cap},${value.comune}`;
      request.incidentEventCategory = incidentEventCategory;
      return Observable.of({}).flatMap(() => {
        return this.service.changeAddress(request).catch(() => {
          this.notificationService.showInfoMessage(errorMessage);
          return Observable.of({status: ServiceResponseStatus.KO} as IncidentEventResponse);
        });
      }).flatMap(response => {
        if (response.status === ServiceResponseStatus.OK) {
          this.notificationService.showSuccessMessage(successMessage);
          return this.service.getUserData();
        }
        return Observable.empty();
      }).flatMap(response => {
        this.userActions.userInfoLoaded(response as UserData);
        return Observable.empty();
      });
    };
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userInfoSubscription, this.clientOffersSubscription]);
  }

}
