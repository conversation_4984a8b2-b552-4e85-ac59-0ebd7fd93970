<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding' *ngFor="let podDetailItem of voipData">
  <p class='sub-title sub-block'>
    Procedere alla configurazione VOIP da effettuare per ciascun numero di telefono attivo:
  </p>
  <section class='col-xs-12 col-sm-12 col-sm-12 col-xs-12 no-padding block '>
    <!--<p class="name"><b>Connessione PPP over Ethernet over ATM (PPPoE) sulla linea ADSL</b></p>-->
    <div>
      <div class="col-xs-12 col-sm-12 col-sm-12 col-xs-12 voip-data no-padding left-padding">
        <div class="col-xs-12 col-sm-12 col-md-4 box-title"><b>1) Numero di telefono</b></div>
        <p class="col-xs-12 col-sm-12 col-md-6 box">{{podDetailItem.dialer}} </p>
      </div>
      <div class="col-xs-12 col-sm-12 col-sm-12 col-xs-12 voip-data no-padding left-padding">
        <div class="col-xs-12 col-sm-12 col-md-4 box-title"><b>2) DTMF</b></div>
        <p class="col-xs-12 col-sm-12 col-md-6 box disabled" id="dtmf">RFC 2833
        </p>
      </div>
      <div class="col-xs-12 col-sm-12 col-sm-12 col-xs-12 voip-data no-padding left-padding">
        <div class="col-xs-12 col-sm-12 col-md-4 box-title"><b>3) Opzione da abilitare</b></div>
        <p class="col-xs-12 col-sm-12 col-md-6 box disabled" id="option">FAX T.38
        </p>
      </div>
      <div class="col-xs-12 col-sm-12 col-sm-12 col-xs-12 voip-data no-padding left-padding">
        <div class="col-xs-12 col-sm-12 col-md-4 box-title"><b>4) Indirizzo proxy server</b></div>
        <p class="col-xs-12 col-sm-12 col-md-6 box disabled" id="proxy">ssw.optimaitalia.com (porta -> 5060)
        </p>
      </div>
      <div class="col-xs-12 col-sm-12 col-sm-12 col-xs-12 voip-data no-padding left-padding">
        <div class="col-xs-12 col-sm-12 col-md-4 box-title"><b>5) Username SIP</b></div>
        <p class="col-xs-12 col-sm-12 col-md-6 box " id="username3">
          {{podDetailItem.credenzialiSIP.username?podDetailItem.credenzialiSIP.username:'username'}}
        </p>
      </div>
      <div class="col-xs-12 col-sm-12 col-sm-12 col-xs-12 voip-data no-padding left-padding">
        <div class="col-xs-12 col-sm-12 col-md-4 box-title"><b>6) Password SIP</b></div>
        <p class="col-xs-12 col-sm-12 col-md-6 box" id="password3">
          {{podDetailItem.credenzialiSIP.password?podDetailItem.credenzialiSIP.password:'password'}} </p>
      </div>
      <div class="col-xs-12 col-sm-12 col-sm-12 col-xs-12 voip-data no-padding left-padding">
        <div class="col-xs-12 col-sm-12 col-md-4 box-title"><b>7) Codec utilizzati</b></div>
        <p class="col-xs-12 col-sm-12 col-md-6 box disabled" id="codec"> G729, G711aLaw </p>
      </div>
      <div class="col-xs-12 col-sm-12 col-sm-12 col-xs-12 no-padding left-padding">
        <div class="col-xs-12 col-sm-12  box-title "><b>8) Il servizio VOIP deve essere associato all’interfaccia WAN
          secondaria</b></div>
      </div>

    </div>

  </section>
  <div>
  </div>
</div>
