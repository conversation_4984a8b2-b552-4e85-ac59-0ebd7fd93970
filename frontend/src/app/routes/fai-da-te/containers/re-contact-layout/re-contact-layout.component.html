<div *ngIf="activeButton===null || activeButton; else requested"
     class="col-lg-12 col-md-12 col-xs-12 col-sm-12 recontact-layout no-padding-mobile" style="display: grid"
     [formGroup]="formGroup">

  <div class="page-title">
    <div class="title-image"></div>
    <div class="title-text"> PARLA CON UN ESPERTO</div>
  </div>

  <div class="row recontact-wrapper no-side-margin" style="display: flex">
    <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 mobile-block" style=" padding-right: 0px;">
      <!--no-side-padding-->
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 input-row no-padding">

        <div class="col-lg-12 col-md-12 no-padding">

          <div class="col-lg-12 col-md-12 no-padding aux-top-margin">
            <div class="input-label">Il tuo numero:</div>
            <div class="col-md-4 no-padding-mobile" style="padding-right: 0; padding-left: 5px">
              <app-input name="phoneNumber" [formGroup]="formGroup"></app-input>
            </div>
          </div>

          <div class="col-lg-12 col-md-12 no-padding aux-top-margin">
            <div class="input-label">Quando vuoi essere richiamato:</div>
            <div class="col-lg-3 col-md-3 no-padding-mobile" style="padding-right: 0; padding-left: 5px">
              <div class="contract-time">
                <app-select name="contactTime" [formGroup]="formGroup" [optionsMap]="contactTimeOptions">
                </app-select>
              </div>
              <app-info class="contact-time-info">
                <info-button><i class="info-circle">i</i></info-button>
                <info-message>
                  <div class="info-title">
                    Un nostro esperto proverà a contattarti nella fascia oraria da te indicata appena possibile.
                    La chiamata sarà effettuata negli orari di apertura del Servizio Clienti.
                  </div>
                </info-message>
              </app-info>
            </div>
          </div>

        </div>

      </div>
      <div class="col-lg-8 col-lg-offset-2 col-md-8 col-md-offset-2 col-sm-12 col-xs-12 confirm-message-block"
           *ngIf="formGroup.controls['contactTime'].hasError('expired')">
        <p class="text-danger">ATTENZIONE:</p>
        <p>L'orario selezionato è in scadenza! La chiamata potrebbe essere spostata a domani. Confermi la fascia
          oraria?</p>
        <p>
          <button class="accept-button-default"
                  (click)="confirmWindowActions.declineContactTime(formGroup.controls['contactTime'])">ANNULLA
          </button>
          <button class="accept-button-default send"
                  (click)="confirmWindowActions.acceptContactTime(formGroup.controls['contactTime'])">CONFERMA
          </button>
        </p>
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 reason-block">
        <p class="margin-top-2">
          <app-select name="reason" [formGroup]="formGroup"
                      [options]="reasonOptions"
                      emptyLabel="Scegli con chi parlare"></app-select>
        </p>
        <p class="margin-top-2">
          <app-input name="message" [formGroup]="formGroup" type="area"
                     placeholder="Qui puoi aggiungere un messaggio alla tua richiesta"></app-input>
        </p>
        <div class="button-group clearfix">
          <button class="accept-button-default" (click)="resetForm()">ANNULLA</button>
          <button class="accept-button-default send" (click)="apply()" [disabled]="!activeButton">INVIA</button>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 no-side-padding no-expert-mobile">
      <img src="../../../../../assets/img/agents/esperto_dash_button.png">
    </div>
  </div>
</div>
<ng-template #requested>
  <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 recontact-layout no-padding-mobile">
    <p class="unActiveText">
      Risulta una tua richiesta di ricontatto in gestione, sarai contattato quanto prima nella fascia oraria e al
      recapito da te indicati. Grazie.
    </p>
  </div>
</ng-template>
