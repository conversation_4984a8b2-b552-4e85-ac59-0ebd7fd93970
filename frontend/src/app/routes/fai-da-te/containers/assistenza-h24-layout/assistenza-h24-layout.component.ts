import {Component, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';

@Component({
  selector: 'app-assistenza-h24-layout',
  templateUrl: './assistenza-h24-layout.component.html',
  styleUrls: ['./assistenza-h24-layout.component.scss']
})
export class AssistenzaH24LayoutComponent implements OnInit {

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;
  status: string;
  dataActivation: Date;

  constructor() {
    this.serviceData.subscribe(data => {
      data.services.forEach(service => {
        if (service.serviceName === 'Assistenza H24') {
          this.status = service.utilities[0].status;
          this.dataActivation = new Date(service.utilities[0].startDate);
        }
      });
    });
  }

  ngOnInit() {
  }
}
