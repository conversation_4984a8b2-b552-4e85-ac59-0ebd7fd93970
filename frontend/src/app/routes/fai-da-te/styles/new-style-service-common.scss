@import "../../../shared/styles/colors";

.page-title-style {
  background: #ffffff;
  border: 2.5px solid #B0C7DD;
  border-radius: 10px;
  height: 70px;
  display: flex;
  align-items: center;
  padding-left: 2%;

  .title-text {
    margin-left: 15px;
    font-weight: bold;
    color: #36749A;
    font-size: 15px;
  }
}

.mainBlock {
  color: #36749A;
  border-radius: 10px;
  border: 1px solid #B0C7DD;
  background: #F0F4F8;
  padding: 25px;
  margin-top: 90px;
  font-size: 15px;
}

button {
  border-radius: 10px;
  border: 1px solid #36749A;
  padding: 5px 15px;
  color: #36749A;
  display: inline-block;
  margin-top: 2%;
  background: white;
  font-size: 12px;
}

.disattiva-btn {
  background: #36749A;
  color: white;
  margin-top: 1%;
}

.no-margin {
  margin: 0;
}

hr {
  height: 1px;
  border-width: 0;
  background-color: #B0C7DD;
  margin-top: 1.5%;
  margin-bottom: 1.5%;
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

@media only screen and (max-width: 991px) {
  .mainBlock {
    margin-top: 2%;
    background: white;
  }
  .page-title-style {
    margin-top: 2%;
  }
}
