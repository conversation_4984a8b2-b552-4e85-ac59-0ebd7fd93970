<div class="col-lg-12 col-md-12 col-sm-12 active-services-layout">
  <div class="col-lg-12 col-md-12 page-title">
    <div class="title-image"></div>
    <div class="title-text">I TUOI SERVIZI</div>
  </div>
  <div *ngIf="!(userServices.length>0)" class="class col-lg-12 no-result">
    <h4>Non risulta alcuna utenza attiva o in attivazione</h4>
  </div>
  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding service-cards">
    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 service-card" *ngFor="let service of userServices">
      <!-- Team Panel-->
      <div class="panel b {{activeServices[service.serviceName]?'able':'disable'}}">
        <div class="panel-heading text-bold" [ngClass]="getCardStatus(service)">
          <div class="row card-header">
            <div class="app--topBlock-icon topBlock"
                 [ngClass]="[serviceIcon(service.serviceName), 'service-icon']"></div>
            <!--<div class="app&#45;&#45;topBlock-title text topBlock service-status">{{ service.serviceName.toLowerCase() !==
            'adsl' ? service.serviceName : 'INTERNET' }}
            </div>-->
            <div class="app--topBlock-title text topBlock service-status">{{setServiceName(service.serviceName)}}</div>
          </div>
          <div class="row">
            <div class="total">
              <div class="subBlock plainBlock active" *ngFor="let utility of service.utilities">
                <p *ngIf="service.serviceName!=='MOBILE' && service.serviceName!=='SERVIZI'">
                  <b>Stato:</b> {{utility.status}}</p>
                <p *ngIf="service.serviceName==='MOBILE'"><b>Stato:</b> {{mobileStatusDecode(utility.status)}}</p>
                <p><b>{{getName(service.serviceName)}} </b> {{utility.utNumber}}</p>
                <p class="addition"
                   *ngIf="serviceUtils.isLuce(service)&&lucePodDetails&&lucePodDetails[utility.utNumber]">
                  <b>Indirizzo: </b>
                  {{lucePodDetails[utility.utNumber].sedeOperativa}}
                </p>
                <p class="addition"
                   *ngIf="serviceUtils.isGas(service)&&gasPodDetails&&gasPodDetails[utility.utNumber]?.sedeOperativa[0]">
                  <b>Indirizzo: </b>
                  {{gasPodDetails[utility.utNumber].sedeOperativa[0].indirizzo}},
                  {{gasPodDetails[utility.utNumber].sedeOperativa[0].comune}},
                  {{gasPodDetails[utility.utNumber].sedeOperativa[0].provincia}},
                  {{gasPodDetails[utility.utNumber].sedeOperativa[0].cap}}
                </p>
                <p class="addition"
                   *ngIf="serviceUtils.isInternet(service)&&adslPodDetails&&adslPodDetails[utility.utNumber]?.sede">
                  <b>Indirizzo: </b>
                  {{adslPodDetails[utility.utNumber].sede.cap}},
                  {{adslPodDetails[utility.utNumber].sede.descrizioneSede}}
                </p>
                <p class="addition"
                   *ngIf="serviceUtils.isFisso(service)&&fissoPodDetails&&fissoPodDetails[utility.utNumber]?.variazioniLinea[0]?.sede">
                  <b>Indirizzo: </b>
                  {{fissoPodDetails[utility.utNumber].variazioniLinea[0].sede.descrizioneSede}}
                </p>
                <p
                  *ngIf="(service.serviceName==='ADSL' || service.serviceName==='VOIP') && utility.status ==='INATTIVABILE'">
                  <b>Motivazione:</b> {{utility.scrap}} </p>
                <div *ngIf="service.serviceName ==='SERVIZI'">
                  <p>{{utility.scrap}}</p>
                  <a class="button-servizi" (click)="redirect(utility.scrap, '')">ENTRA</a>
                </div>
                <div *ngIf="utility.utNumber && shouldShowEntra(utility)">
                  <a class="button" (click)="redirect(service.serviceName, utility.utNumber)">ENTRA</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

