@import "~app/shared/styles/colors";

.router-layout {

  .page-title {
    .title-image {
      background: url("/assets/img/optima/Set_Icone_AreaClienti_Router.png") no-repeat center;
      background-size: contain;
      width: 68px;
      height: 50px;
      float: left;
    }
  }

  .main {
    margin-top: 2%;
    width: 100%;
    border-radius: 5px;
    border: 1px solid $menu-border;
    padding-bottom: 20px;
  }
}

.container {
  width: 95%;
  margin: auto;
}

.header {
  border-bottom: 1px solid $menu-border;
  margin-bottom: 5px;
}

.title {
  color: $dark-blue;
  padding-top: 15px;
  padding-bottom: 15px;
  font-weight: bold;
  font-size: 16px;
}

.name {
  padding: 5px;
  color: $dark-blue;
  font-size: 15px;
}

.button {
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 5px;
  color: $dark-blue;
  text-align: center;
  border: 1px solid $menu-border;
  margin: 0 15px 15px 0;
  font-size: 15px;
  height: 44px;
}

.active {
  background: #336D94;
  color: white;
}

input {
  padding: 5px;
  border-radius: 5px;
  border: 1px solid $menu-border;
}

.col-md-3 {
  margin: 5px 0;
}

.box-title {
  padding: 5px 10px;

}

.sub-block {
  margin-top: 20px;
}

.sub-title {
  font-size: 17px;
}

.left-padding {
  margin-left: 0;
}

.numero {
  line-height: 40px;
  padding: 10px 2.5%;
  color: #336D94;
  font-size: 16px;
}

select {
  padding: 5px;
  background: white;
  border: 1px solid $menu-border;
  border-radius: 5px;
  color: $dark-blue;
}

@media only screen and (max-width: 991px) {
  .main {
    width: 100%;
  }
  .router-layout {
    padding: 0;

    .main, .header {
      border: none;
    }

    .header, .title {
      padding: 0;
    }

    .page-title {
      display: none;
    }
  }
  .header:first-child {
    border-bottom: none;
  }

}
