<div class="wrapper">
    <div class="block-center mt-xl wd-xl">
        <!-- START panel-->
        <div class="panel panel-dark panel-flat">
            <div class="panel-heading text-center">
                <a href="javascript: void(0)">
                    <img class="block-center img-rounded" src="assets/img/old-logo/logo.png" alt="Image" />
                </a>
            </div>
            <div class="panel-body">
                <p class="text-center pv">SIGNUP TO GET INSTANT ACCESS.</p>
                <form [formGroup]="valForm" class="form-validate mb-lg" role="form" name="registerForm" novalidate="" (submit)="submitForm($event, valForm.value)">
                    <div class="form-group has-feedback">
                        <label class="text-muted">Email address</label>
                        <input class="form-control" type="email" name="account_email" placeholder="Enter email" autocomplete="off" formControlName="email" required="" />
                        <span class="fa fa-envelope form-control-feedback text-muted"></span>
                        <span class="text-danger" *ngIf="valForm.controls['email'].hasError('required') && (valForm.controls['email'].dirty || valForm.controls['email'].touched)">Campo Obbligatorio</span>
                        <span class="text-danger" *ngIf="valForm.controls['email'].hasError('email') && (valForm.controls['email'].dirty || valForm.controls['email'].touched)">This field must be a valid email address</span>
                    </div>
                    <div formGroupName="passwordGroup">
                        <div class="form-group has-feedback">
                            <label class="text-muted">Password</label>
                            <input class="form-control" id="id-password" type="password" name="password" formControlName="password" [formControl]="valForm.get('passwordGroup.password')"/>
                            <span class="fa fa-lock form-control-feedback text-muted"></span>
                            <span class="text-danger" *ngIf="valForm.get('passwordGroup.password').hasError('required') && (valForm.get('passwordGroup.password').dirty || valForm.get('passwordGroup.password').touched)">Campo Obbligatorio</span>
                            <span class="text-danger" *ngIf="valForm.get('passwordGroup.password').hasError('pattern') && (valForm.get('passwordGroup.password').dirty || valForm.get('passwordGroup.password').touched)">Input should match 'a-zA-Z0-9' and 6-10 length</span>
                        </div>
                        <div class="form-group has-feedback">
                            <label class="text-muted">Retype Password</label>
                            <input class="form-control" type="password" name="confirmPassword" formControlName="confirmPassword" [formControl]="valForm.get('passwordGroup.confirmPassword')"/>
                            <span class="fa fa-lock form-control-feedback text-muted"></span>

                            <span class="text-danger" *ngIf="valForm.get('passwordGroup.confirmPassword').hasError('equalTo')">Password does Not match</span>
                        </div>
                    </div>
                    <div class="clearfix">
                        <div class="checkbox c-checkbox pull-left mt0">
                            <label>
                                <input type="checkbox" required="" name="account_agreed" formControlName="accountagreed" />
                                <span class="fa fa-check"></span>I agree with the <a href="javascript: void(0)">terms</a>
                            </label>
                        </div>
                    </div>
                    <span class="text-danger" *ngIf="valForm.controls['accountagreed'].hasError('required') && (valForm.controls['accountagreed'].dirty || valForm.controls['accountagreed'].touched)">You must agree the terms</span>
                    <button class="btn btn-block btn-primary mt-lg" type="submit">Create account</button>
                </form>
                <!-- <div class="alert alert-danger text-center"></div> -->
                <p class="pt-lg text-center">Have an account?</p><a class="btn btn-block btn-default" [routerLink]="'/login'">Signup</a>
            </div>
        </div>
        <!-- END panel-->
        <div class="p-lg text-center">
            <span>&copy;</span>
            <span>{{ settings.app.year }}</span>
            <span>-</span>
            <span>{{ settings.app.name }}</span>
            <br/>
            <span>{{ settings.app.description }}</span>
        </div>
    </div>
</div>
