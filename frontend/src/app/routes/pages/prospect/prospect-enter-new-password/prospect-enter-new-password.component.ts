import {Component, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ActivatedRoute} from '@angular/router';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {ProspectUserService} from '../../../../common/services/prospect-user/prospect-user.service';

@Component({
  selector: 'app-prospect-enter-new-password',
  templateUrl: './prospect-enter-new-password.component.html',
  styleUrls: ['./prospect-enter-new-password.component.scss']
})
export class ProspectEnterNewPasswordComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  token: string;
  passwordsForm: FormGroup;
  isTokenExpired = true;
  success = false;
  error = false;

  constructor(private route: ActivatedRoute, private fb: FormBuilder, private prospectUserService: ProspectUserService) {
    this.token = this.route.snapshot.params['token'];
    this.prospectUserService.isTokenAvailable(this.token).subscribe(
      response => this.isTokenExpired = !response,
      _ => this.isTokenExpired = true
    );
    this.passwordsForm = fb.group({
      'passwords': fb.group({
        password1: ['', [Validators.required, Validators.pattern('^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$')]],
        password2: ['', [Validators.required, Validators.pattern('^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$')]]
      }, {validator: this.matchValidator})
    });
  }

  ngOnInit() {
  }

  matchValidator(group: FormGroup) {
    const password1 = group.get('password1').value;
    const password2 = group.get('password2').value;
    return password1 === password2 ? null : {mismatch: true};
  }

  submitForm($ev) {
    $ev.preventDefault();
    Object.keys(this.passwordsForm.controls).forEach((_) => {
      FormUtils.setFormControlsAsTouched(this.passwordsForm);
    });
    if (this.passwordsForm.valid) {
      this.prospectUserService.setNewPassword(this.passwordsForm.value, this.token).subscribe(response => {
          this.success = response.status === 200;
          this.error = !this.success;
        }
      );
    }
  }
}
