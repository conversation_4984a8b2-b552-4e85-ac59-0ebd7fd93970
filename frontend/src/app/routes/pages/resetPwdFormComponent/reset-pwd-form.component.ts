import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {ForgotPasswordService} from '../../../common/services/forgotPassword/forgot-password.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {FormUtils} from '../../../common/utils/FormUtils';


@Component({
  selector: 'app-reset-form-pwd',
  templateUrl: './reset-pwd-form.component.html',
  styleUrls: ['./reset-pwd-form.component.scss']
})
export class ResetPwdFormComponent implements OnInit {
  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  token: string;
  isTokenExpired = true;
  success = false;
  error = false;
  errorComparative = false;
  valForm: FormGroup;

  constructor(private route: ActivatedRoute, private checkPswService: ForgotPasswordService, fb: FormBuilder) {
    this.token = this.route.snapshot.params['token'];
    this.valForm = fb.group({
      'passwords': fb.group({
        password1: ['', [Validators.required, Validators.pattern('^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$')]],
        password2: ['', [Validators.required, Validators.pattern('^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$')]]
      }, {validator: this.matchValidator})
    });
    this.valForm.valueChanges.subscribe(() => {
      this.errorComparative = false;
    });
    checkPswService.isTokenAvaliable(this.token).subscribe(data => {
        if (data.length > 0) {
          this.isTokenExpired = false;
        }
      },
      error => {
        this.isTokenExpired = true;
      });
  }


  matchValidator(group: FormGroup) {
    if (group.controls['password1'].value === group.controls['password2'].value) {
      return null;
    }
    return {
      mismatch: true
    };
  }

  submitForm($ev, value: any) {
    $ev.preventDefault();
    Object.keys(this.valForm.controls).forEach((c) => {
      FormUtils.setFormControlsAsTouched(this.valForm);
    });
    if (this.valForm.valid) {
      this.checkPswService.setPsw(value, this.token).subscribe(data => {
        if (data.status === 200) {
          this.success = true;
        }
        if (data.status === 409) {
          this.errorComparative = true;
        }
        if (data.status === 400 || data.status === 404) {
          this.error = true;
        }
      });
    }
  }

  ngOnInit() {
  }
}
