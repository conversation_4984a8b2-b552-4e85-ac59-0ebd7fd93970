<div [formGroup]="formGroupPayPal">
  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 recharge-sim-title">
    <div>Scegli l'importo da ricaricare su una o più SIM Optima.</div>
    <div>Potrai inserire il tuo numero o i numeri Optima dei tuoi amici.</div>
  </div>


  <div class="col-lg-5 col-md-5 col-sm-5 no-padding position">
    <label for="simList">Seleziona una delle tue SIM.</label>
    <select id="simList" class="form-control" formControlName="msisdnId">
      <option value=""></option>
      <option *ngFor="let record of contractRecords | async" [attr.value]="record.msisdnId"
              [selected]="record.msisdnId===formGroupPayPal.controls['msisdnId'].value">
        SIM {{record.msisdnId}}
      </option>
    </select>
  </div>

  <div class="col-lg-5 col-md-7 col-sm-7 col-xs-12 optima-number-block">
    <label for="simNumber">Scrivi il numero Optima che desideri ricaricare.</label>

    <div class="form-inline" style="display: flex">
      <input id="simNumberTemplate" class="form-control form-group" type="text" value="+39" disabled>
      <input id="simNumber" class="form-control form-group" style="width: -webkit-fill-available;" type="number"
             placeholder="Numero Optima"
             formControlName="optimaNumber">
    </div>


    <span class="text-danger" *ngIf="formGroupPayPal.controls['optimaNumber'].hasError('minLength') &&
       (formGroupPayPal.controls['optimaNumber'].dirty || formGroupPayPal.controls['optimaNumber'].touched)">
        Lunghezza minima 6 caratteri.
      </span>
    <span class="text-danger"
          *ngIf="formGroupPayPal.controls['optimaNumber'].hasError('numberExistence') &&
       (formGroupPayPal.controls['optimaNumber'].dirty || formGroupPayPal.controls['optimaNumber'].touched)">Il numero selezionato non è un numero appartenente alla rete Optima.</span>
    <span class="text-danger"
          *ngIf="formGroupPayPal.controls['optimaNumber'].hasError('maxLength') && (formGroupPayPal.controls['optimaNumber'].dirty ||
      formGroupPayPal.controls['optimaNumber'].touched)">Lunghezza massima 10 caratteri.</span>
    <span class="text-danger"
          *ngIf="(formGroupPayPal.controls['msisdnId'].hasError('required') && (formGroupPayPal.controls['msisdnId'].dirty ||
formGroupPayPal.controls['msisdnId'].touched))||(formGroupPayPal.controls['optimaNumber'].hasError('required') &&
(formGroupPayPal.controls['optimaNumber'].dirty ||formGroupPayPal.controls['optimaNumber'].touched))">
Inserisci il numero di telefono da ricaricare</span>
  </div>

  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding importo">
    <div class="col-lg-5 col-md-5 col-xs-12 no-padding">
      <label class="importo-label" for="importo">Importo</label>
      <select id="importo" class="form-control" formControlName="mooneyValue">
        <option *ngFor="let amount of amounts" [selected]="amount===formGroupPayPal.controls['mooneyValue'].value"
                [attr.value]="amount">{{amount}} euro
        </option>
      </select>
      <button class="button payment-button" (click)="checkForm()">Paga con PayPal</button>
    </div>
  </div>

</div>
