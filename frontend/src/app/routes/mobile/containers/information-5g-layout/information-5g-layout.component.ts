import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {OffersInAppService} from '../../../offers-in-apps/service/offers-in-app.service';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {AddOn, Offer5GResponse, Sim} from '../../../offers-in-apps/models/Offer5GModels';

enum ViewLayoutMode {
  ACTIVATION_WITH_DELAY = 'activationWithDelay',
  ACTIVATION_IMMEDIATE = 'activationImmediate',
}

enum ViewModalMode {
  CONTRACT_INFO = 'contractInfo',
  ACTIVATION_CONFIRMATION = 'activationConfirmation',
  ACTIVATION_ALREADY_IN_PROGRESS = 'activationAlreadyInProgress'
}

@Component({
  selector: 'app-information-5g-layout',
  templateUrl: './information-5g-layout.component.html',
  styleUrls: ['./information-5g-layout.component.scss']
})
export class Information5gLayoutComponent implements OnInit {

  viewMode: ViewLayoutMode;
  ViewModeLayout = ViewLayoutMode;
  viewModalMode: ViewModalMode;
  ViewModeModal = ViewModalMode;
  selectedSim: Sim;
  allSims: Sim[] = [];
  showModal: boolean = false;
  isClosing: boolean = false;
  showGeneralErrorWindow: boolean = false;
  targetMsisdnId: string;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly offerService: OffersInAppService,
    private readonly incidentEventService: IncidentEventService
  ) {
  }

  ngOnInit() {
    this.targetMsisdnId = this.activatedRoute.snapshot.params['id'];
    this.loadOffer5GData('667288', '121');
  }

  loadOffer5GData(clientId: string, subscriptionId: string) {
    this.offerService.getOffer5GData(clientId, subscriptionId).subscribe((data: Offer5GResponse) => {
      this.allSims = data.sim;
      // Find SIM by msisdnId from URL
      const targetSim = this.allSims.find(sim => sim.msisdnId === this.targetMsisdnId);
      if (targetSim) {
        this.selectSim(targetSim);
      } else if (this.allSims.length > 0) {
        // Fallback to first SIM if target not found
        this.selectSim(this.allSims[0]);
      }
    }, () => {
      this.showGeneralErrorWindow = true;
    });
  }

  selectSim(sim: Sim) {
    this.selectedSim = sim;
    const currentAddOn = sim.addOnAviable && sim.addOnAviable.find(addon => addon.codice === '5G');
    if (currentAddOn) {
      if (currentAddOn.numRichiesteAttivazioni === 0) {
        this.viewMode = ViewLayoutMode.ACTIVATION_IMMEDIATE;
      } else {
        this.viewMode = ViewLayoutMode.ACTIVATION_WITH_DELAY;
      }
    }
  }

  onSimChange(event: any) {
    const selectedMsisdnId = event.target.value;
    const sim = this.allSims.find(s => s.msisdnId === selectedMsisdnId);
    if (sim) {
      this.selectSim(sim);
    }
  }

  get current5GAddOn(): AddOn | undefined {
    return this.selectedSim && this.selectedSim.addOnAviable && this.selectedSim.addOnAviable.
    find(addon => addon.codice === '5G');
  }

  openModal(): void {
    this.viewModalMode = ViewModalMode.CONTRACT_INFO;
  }

  closeModal(): void {
    this.viewModalMode = null;
  }

  activateNow(): void {
    this.closeModal();
    if (this.current5GAddOn && this.current5GAddOn.richiestaInCorso) {
      this.viewModalMode = ViewModalMode.ACTIVATION_ALREADY_IN_PROGRESS;
      return;
    }
    if (this.current5GAddOn) {
      this.incidentEventService.openIncidentEventoForActivate5GOffer(
        this.selectedSim.subscriptionId,
        this.current5GAddOn.codiceOfferta,
        this.current5GAddOn.canoneMese
      ).subscribe(response => {
        if (response.status === 'OK') {
          this.viewModalMode = ViewModalMode.ACTIVATION_CONFIRMATION;
        } else {
          this.showGeneralErrorWindow = true;
        }
      });
    }
  }

  reloadPage() {
    window.location.reload();
  }
}
