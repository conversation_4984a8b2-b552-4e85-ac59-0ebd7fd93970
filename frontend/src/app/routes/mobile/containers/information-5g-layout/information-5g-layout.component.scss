:host {
  font-size: 15px;
  color: #36749A;
}

.sim-selection {
  display: flex;
  align-items: center;
  gap: 23px;

  label {
    font-weight: bold;
    margin: 0;
  }

  select {
    -webkit-appearance: none; /*Removes default chrome and safari style*/
    -moz-appearance: none;
    background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
    background-position-x: 95%;
    width: 247px;
    height: 35px;
    border: 1px solid #B2C8DD;
    border-radius: 5px;
    padding-left: 15px;
    font-weight: bold;
  }
}

.activation-info {
  padding: 30px;

  .icon-text-container {
    display: flex;
    align-items: center;
    gap: 23px;
    margin: 25px 0;

    .icon-5g {
      width: 54px;
      height: 54px;
    }

    .info-text {
      width: 300px;
      font-weight: bold;
    }
  }
}

.additional-info {
  margin-bottom: 20px;
  width: 555px;
  font-style: italic;
  max-width: 70vw;
}

.activate-button {
  background: #36749A;
  color: white;
  padding: 11px 19px;
  border: 1px solid #36749A;
  border-radius: 10px;
  font-weight: bold;
  cursor: pointer;
  font-size: 11px;
}

// Modal styles
.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  backdrop-filter: blur(30px);
  overflow: hidden;
  overflow-y: scroll;
}

.inner-modal-div {
  margin: auto;
  top: 13vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #B0C7DD;
  position: relative;
  padding: 50px;
  width: 690px;
  max-width: 95%;
  max-height: 80vh;
}


.modal-info {
  text-align: center;

  .modal-title {
    margin-bottom: 30px;
    font-size: 20px;
    font-weight: bold;
  }

  .contract-content {
    text-align: left;
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 30px;
    padding: 0 20px;
    overflow-x: hidden;
    word-wrap: break-word;

    h3 {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0 10px 0;
    }

    p {
      font-size: 14px;
      line-height: 1.4;
      margin-bottom: 15px;
    }

    strong {
      font-weight: bold;
    }

    em {
      font-style: italic;
    }
  }

  .modal-text {
    margin-bottom: 30px;
    font-style: italic;
  }
}

.modal-button {
  font-size: 14px;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.result-modal-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 35px;
  text-align: center;

  .icon-ok, .icon-warning {
    width: 120px;
    height: 120px;
  }

  h2 {
    font-weight: bold;
    margin: 0;
  }

  h3 {
    font-size: 25px;
    margin: 0;
  }

  h4 {
    font-size: 22px;
    margin: 0;
    font-style: italic;
  }
}

@media screen and (max-width: 991px) {
  .sim-selection {
    gap: 10px;
    flex-direction: column;
  }

  .inner-modal-div {
    position: absolute;
    top: auto;
    bottom: 0;
    margin: 0;
    width: 100%;
    max-width: 100%;
    height: 80vh;
    border-radius: 30px 30px 0 0;
    border: 0;
    transform: translateY(100%);
    animation: slideUp 0.5s ease-out 0.1s forwards;
    display: flex;
    flex-direction: column;
    box-shadow: 0px -3px 6px #00000029;
    padding: 40px;
  }

  .fa-times {
    font-size: 30px;
  }

  .modal-info {
    .modal-text, .contract-content, .modal-title {
      margin-bottom: 20px;
    }
  }
  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
}
