import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs/Observable';
import { Offer5GResponse } from '../models/Offer5GModels';

@Injectable()
export class OffersInAppService {

  constructor(private readonly http: HttpClient) {
  }

  public getOffer5GData(clientId: string, subscriptionId: string): Observable<Offer5GResponse> {
    return this.http.get<Offer5GResponse>(`/api/offers-in-apps/offer-5g/${clientId}/${subscriptionId}`);
  }
}
