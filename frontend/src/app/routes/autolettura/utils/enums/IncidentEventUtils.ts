import * as moment from 'moment';
import { AutoLetturaForm } from '../../model/AutoLetturaForm';

const incidentAnnotationSuffixes = {
  GAS: (fomData: any) => 'Contatore con correttore SI',
  ENERGIA: (fomData: any) => 'Contatore elettronico'
};

const dataBuilder = {
  GAS: (formData: any) => {
    const consumptions = [];
    Object.keys(formData.gas).forEach((key) => {
      consumptions.push(formData.gas[key]);
    });
    return consumptions;
  }
  ,
  ENERGIA: (formData: any) => {
    const consumptions = [];
    formData.consumptions.forEach((value) => {
      if (value.consumption) {
        consumptions.push(value.consumption);
      }
    });
    return consumptions;
  }
};

export class IncidentEventUtils {

  static podOrPDR(key: string): string {
    if (key) {
      return new RegExp('^[0-9]*$').test(key) ? 'PDR' : 'POD';
    }
    return null;
  }

  static buildIncidentAnnotation(formData: AutoLetturaForm): string {
    if (formData) {
      const date = moment().format('YY/MM/DD');
      return `Richiesta inserimento autolettura ${dataBuilder[formData.type](formData)} ${date}\n${
        formData.contatoreType}`;
        // IncidentEventUtils.getIncidentAnnotationSuffix(formData)}`;
    }
    return null;
  }

  static getIncidentAnnotationSuffix(formData: AutoLetturaForm): string {
    return incidentAnnotationSuffixes[formData.type] ? incidentAnnotationSuffixes[formData.type](formData) : '';
  }

}
