import CIDateTime from './CIDateTime';


export interface CustomerInfo {
  id: number;
  title: string;
  firstName: string;
  lastName: string;
  username: string;
  registerDateregisterDate: CIDateTime;
  addressList: ArrayOfCIAddressReadType;
  phoneNumberList: ArrayOfCIPhoneNumberReadType;
  emailAddressList: ArrayOfCIEmailAddressReadType;
  customFieldList: ArrayOfCICustomFieldReadType;
  defaultAddress: CIAddressReadType;
  defaultPhoneNumber: CIPhoneNumberReadType;
  defaultEmailAddress: CIEmailAddressReadType;
}


export interface CIAddressReadType {
  id: number;
  line1: string;
  line2: string;
  line3: string;
  line4: string;
  line5: string;
  zipcode: string;
  country: string;
  defaultAddress: boolean;
}

interface CIPhoneNumberType {
  value: string;
}

interface CIPhoneNumberReadType {
  id: number;
  internationalCode: string;
  areaCode: string;
  number: string;
  doNotCall: boolean;
  defaultPhoneNumber: boolean;
  phoneNumberType: CIPhoneNumberType;
}

interface CIEmailAddressReadType {
  id: number;
  emailAddress: string;
  defaultEmailAddress: boolean;
}

interface CICustomFieldReadType {
  id: number;
  name: string;
  text: string;
  isTextVisible: boolean;
}

export interface ArrayOfCIAddressReadType {
  ciAddressReadType: CIAddressReadType[];
}

export interface ArrayOfCIPhoneNumberReadType {
  ciPhoneNumberReadType: CIPhoneNumberReadType[];
}

export interface ArrayOfCIEmailAddressReadType {
  ciEmailAddressReadType: CIEmailAddressReadType[];
}

interface ArrayOfCICustomFieldReadType {
  ciCustomFieldReadType: CICustomFieldReadType[];
}
