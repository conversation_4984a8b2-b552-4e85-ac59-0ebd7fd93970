.back {
  position: fixed;
  z-index: 1100;
  background-color: rgba(255, 255, 255, 0.9);
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  overflow: hidden;
}

.spinner {
  background: url("/assets/img/logo/optima_new_main_logo.svg") no-repeat;
  width: 200px;
  height: 150px;
  background-size: 100%;

  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -100px;
  margin-left: -75px;
  z-index: 900;

  opacity: 0.2;
  -webkit-animation: example 1s linear infinite;
  -moz-animation: example 1s linear infinite;
  animation: example 1s linear infinite;

}

@media only screen and (max-width: 450px) {
  .spinner {
    left: 43%;
  }
}

/* Safari 4.0 - 8.0 */
@-webkit-keyframes example {

  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

/* Standard syntax */
@keyframes example {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
