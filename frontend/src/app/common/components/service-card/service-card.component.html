<div class="col-lg-6 col-md-6 block inactive-service-card" *ngIf="serviceCard">
  <div class="panel b able">
    <div class="panel-heading text-bold">
      <div class="row card-header" *ngIf="serviceName === 'amazonprime'">
        <div class="app--topBlock-icon topBlock amazon-icon"
             [ngClass]="[serviceIcon, 'service-icon']"></div>
      </div>
      <div class="row card-header" *ngIf="serviceName !== 'amazonprime'">
        <div class="app--topBlock-icon topBlock"
             [ngClass]="[serviceIcon, 'service-icon']"></div>
        <div class="app--topBlock-title text topBlock service-status">
          {{serviceName}}
        </div>
      </div>
      <div class="row">
        <div class="total">
          <div class="subBlock plainBlock active" *ngFor="let utility of serviceCard.utilities">
            <div *ngIf="serviceCard.serviceName !== 'AMAZON'" class="service-pod"><b>{{utilityName}}</b> {{utility.utilityNumber}}</div>
            <div><b>Stato:</b> {{utility.status}}</div>
            <div *ngIf="utility.startDate"><b>Data attivazione:</b> {{utility.startDate | date : 'dd/MM/yyyy'}}</div>
            <div *ngIf="utility.endDate"><b>Data disattivazione:</b> {{utility.endDate | date : 'dd/MM/yyyy'}}</div>
            <div class="addition">
              {{utility.addition}}
            </div>
            <div *ngIf="utility.motivazione"><b>Motivazione:</b> {{utility.motivazione}} </div>
            <div *ngIf="shouldShowEntra(utility)">
              <a class="button" (click)="redirect(serviceName, utility.utilityNumber)">ENTRA</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
