export class MnpActivation {

  customerId: string;
  // user: string;
  // userFullName: string;
  // incidentId: string;
  // parentChangeId: number;
  change: Change;
}

export class Change {

  changeType: number;
  portingNumber: string;
  // portingICCID: string;
  // companyName: string;
  // vatNumber: string;
  firstName: string;
  lastName: string;
  fiscalCode: string;
  sourceSupplierCode: string;
  sourceSupplierDesc: string;
  sourceContractType: string;
  documentType: string;
  documentNumber: string;
  creditTransfer: boolean;
  partialCheck: boolean;
  // theftFlag: boolean;
  dataCut: string;
  // subscriptionId: number;
  msisdn: string;
  // applyDate: string;
  files: Array<File>;
}

export class File {
  nomeFile: string;
  uriSharepoint: string;
}

export class MnpUploadFileResponse {
  Codice: number;
  Descrizione: string;
  ServerRelativeUrl: string;
}
