export enum ServiceStatus {
  DISATTIVATO = 'DISATTIVATO',
  DISATTIVATA = 'DISATTIVATA',
  IN_ATTIVAZIONE = 'IN_ATTIVAZIONE',
  ATTIVATO = 'ATTIVATO',
  ATTIVO = 'Attivo',
  ACTIVE = 'ACTIVE',
  ATTIVA = 'ATTIVA',
  INITIACTIVE = 'INITIACTIVE',
  CANCELLATA = 'CANCELLATA',
  MOB_RICHIESTA_NON_EVASA = 'MOB_RICHIESTA_NON_EVASA',
  INATTIVABILE = 'INATTIVABILE',
  PORTED_OUT = 'Ported Out',
  SOSPESO = 'SOSPESO',
  SOSPESA = 'SOSPESA',
  ATTIVABILE = 'ATTIVABILE',
  SOSTITUITO = 'SOSTITUITO'
}

export enum ActiveServiceStatus {
  ATTIVATO = ServiceStatus.ATTIVATO,
  ATTIVA = ServiceStatus.ATTIVA,
  CANCELLATA = ServiceStatus.CANCELLATA,
  ACTIVE = ServiceStatus.ACTIVE,
  IN_ATTIVAZIONE = ServiceStatus.IN_ATTIVAZIONE,
  INITIACTIVE = ServiceStatus.INITIACTIVE,
  SOSPESO = ServiceStatus.SOSPESO,
  SOSPESA = ServiceStatus.SOSPESA
}

export enum InActiveServiceStatus {
  DISATTIVATO = ServiceStatus.DISATTIVATO,
  DISATTIVATA = ServiceStatus.DISATTIVATA,
  INATTIVABILE = ServiceStatus.INATTIVABILE,
  PORTED_OUT = ServiceStatus.PORTED_OUT
}
