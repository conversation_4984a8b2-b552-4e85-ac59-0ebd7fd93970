import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { PaginationCallback } from './PaginationCallback';

export class Paginator {
  array: Array<any>;
  page: BehaviorSubject<number>;
  step: number;
  onChange: Function;
  hasNext: boolean;
  hasPrevious: boolean;
  reverse: boolean;
  paginationCallback: PaginationCallback;
  pages: any;


  constructor() {

  }

  next() {
    if (this.hasNext) {
      this.page.next(this.page.getValue() + 1);
    }

  }

  previous() {
    if (this.hasPrevious) {
      this.page.next(this.page.getValue() - 1);
    }
  }

  nextValue() {
    return this.paginationCallback.nextValue(this);
  }


  previousValue() {
    return this.paginationCallback.previousValue(this);
  }

  initialize() {
    const callback = this.paginationCallback.paginationCallback(this);
    this.hasNext = this.page.value !== this.pages;
    this.hasPrevious = this.page.value !== 1;
    this.page.subscribe(callback);
  }


  hasNextHasPrevious(nextPage) {
    this.hasNext = this.pages && nextPage !== this.pages;
    this.hasPrevious = nextPage !== 1;
  }

}
